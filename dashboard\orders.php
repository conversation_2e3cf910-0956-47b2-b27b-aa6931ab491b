<?php
require_once '../config/config.php';
require_once '../classes/User.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../auth/login.php');
}

try {
    $db = Database::getInstance();
    
    // استرجاع طلبات المستخدم
    $stmt = $db->query("
        SELECT o.*, s.name as service_name, c.name as category_name
        FROM orders o 
        JOIN services s ON o.service_id = s.id 
        JOIN service_categories c ON s.category_id = c.id
        WHERE o.user_id = ? 
        ORDER BY o.created_at DESC
    ", [$_SESSION['user_id']]);
    
    $orders = $stmt->fetchAll();
    
} catch (Exception $e) {
    $orders = [];
    showMessage('حدث خطأ في استرجاع الطلبات', 'danger');
}

$message = getMessage();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلباتي - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .dashboard-container {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: var(--primary-color);
            color: white;
            padding: 2rem 0;
        }
        
        .sidebar-header {
            text-align: center;
            padding: 0 1rem 2rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 2rem;
        }
        
        .sidebar-menu {
            list-style: none;
        }
        
        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }
        
        .sidebar-menu a {
            display: block;
            padding: 1rem 1.5rem;
            color: white;
            text-decoration: none;
            transition: var(--transition);
        }
        
        .sidebar-menu a:hover {
            background-color: var(--secondary-color);
        }
        
        .sidebar-menu a.active {
            background-color: var(--secondary-color);
        }
        
        .main-content {
            flex: 1;
            padding: 2rem;
            background-color: #f8f9fa;
        }
        
        .page-header {
            background: white;
            padding: 1.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
        }
        
        .orders-container {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            overflow: hidden;
        }
        
        .orders-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            background-color: var(--light-color);
        }
        
        .order-card {
            border-bottom: 1px solid var(--border-color);
            padding: 1.5rem;
            transition: var(--transition);
        }
        
        .order-card:hover {
            background-color: #f8f9fa;
        }
        
        .order-card:last-child {
            border-bottom: none;
        }
        
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .order-number {
            font-weight: 700;
            color: var(--primary-color);
            font-size: 1.1rem;
        }
        
        .order-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            color: white;
        }
        
        .status-pending { background-color: var(--warning-color); }
        .status-processing { background-color: var(--secondary-color); }
        .status-active { background-color: var(--success-color); }
        .status-suspended { background-color: var(--danger-color); }
        .status-cancelled { background-color: #6c757d; }
        
        .order-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .order-detail {
            display: flex;
            flex-direction: column;
        }
        
        .order-detail-label {
            font-size: 0.875rem;
            color: #666;
            margin-bottom: 0.25rem;
        }
        
        .order-detail-value {
            font-weight: 600;
        }
        
        .order-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
        
        .mobile-sidebar-toggle {
            display: none;
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 1001;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.75rem;
            border-radius: 50%;
            cursor: pointer;
        }
        
        @media (max-width: 768px) {
            .dashboard-container {
                flex-direction: column;
            }
            
            .sidebar {
                position: fixed;
                top: 0;
                right: -250px;
                height: 100vh;
                z-index: 1000;
                transition: right 0.3s ease;
            }
            
            .sidebar.active {
                right: 0;
            }
            
            .mobile-sidebar-toggle {
                display: block;
            }
            
            .main-content {
                padding: 4rem 1rem 1rem;
            }
            
            .order-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
            
            .order-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- الشريط الجانبي -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-user-circle"></i></h3>
                <p><?php echo htmlspecialchars($_SESSION['full_name']); ?></p>
                <small><?php echo htmlspecialchars($_SESSION['email']); ?></small>
            </div>
            
            <ul class="sidebar-menu">
                <li><a href="index.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                <li><a href="orders.php" class="active"><i class="fas fa-shopping-cart"></i> طلباتي</a></li>
                <li><a href="invoices.php"><i class="fas fa-file-invoice"></i> الفواتير</a></li>
                <li><a href="support.php"><i class="fas fa-headset"></i> الدعم الفني</a></li>
                <li><a href="profile.php"><i class="fas fa-user-edit"></i> الملف الشخصي</a></li>
                <li><a href="../services.php"><i class="fas fa-plus"></i> طلب خدمة جديدة</a></li>
                <li><a href="../index.php"><i class="fas fa-home"></i> الموقع الرئيسي</a></li>
                <li><a href="../auth/logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
            </ul>
        </aside>
        
        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <button class="mobile-sidebar-toggle" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
            
            <!-- رأس الصفحة -->
            <div class="page-header">
                <h1><i class="fas fa-shopping-cart"></i> طلباتي</h1>
                <p>إدارة ومتابعة جميع طلباتك</p>
            </div>
            
            <!-- عرض الرسائل -->
            <?php if ($message): ?>
                <div class="alert alert-<?php echo $message['type']; ?>">
                    <?php echo $message['message']; ?>
                </div>
            <?php endif; ?>
            
            <!-- قائمة الطلبات -->
            <div class="orders-container">
                <div class="orders-header">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <h3><i class="fas fa-list"></i> قائمة الطلبات</h3>
                        <a href="../services.php" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> طلب خدمة جديدة
                        </a>
                    </div>
                </div>
                
                <?php if (empty($orders)): ?>
                    <div class="p-3 text-center">
                        <i class="fas fa-inbox fa-3x" style="color: var(--border-color); margin-bottom: 1rem;"></i>
                        <h4>لا توجد طلبات حتى الآن</h4>
                        <p>ابدأ بطلب خدمة جديدة من مجموعة خدماتنا المتنوعة</p>
                        <a href="../services.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i> طلب خدمة جديدة
                        </a>
                    </div>
                <?php else: ?>
                    <?php foreach ($orders as $order): ?>
                        <div class="order-card">
                            <div class="order-header">
                                <div class="order-number">
                                    <i class="fas fa-hashtag"></i> <?php echo htmlspecialchars($order['order_number']); ?>
                                </div>
                                <div class="order-status status-<?php echo $order['status']; ?>">
                                    <?php
                                    $statusTexts = [
                                        'pending' => 'في الانتظار',
                                        'processing' => 'قيد المعالجة',
                                        'active' => 'نشط',
                                        'suspended' => 'معلق',
                                        'cancelled' => 'ملغي',
                                        'completed' => 'مكتمل'
                                    ];
                                    echo $statusTexts[$order['status']] ?? $order['status'];
                                    ?>
                                </div>
                            </div>
                            
                            <div class="order-details">
                                <div class="order-detail">
                                    <div class="order-detail-label">الخدمة</div>
                                    <div class="order-detail-value"><?php echo htmlspecialchars($order['service_name']); ?></div>
                                </div>
                                
                                <div class="order-detail">
                                    <div class="order-detail-label">الفئة</div>
                                    <div class="order-detail-value"><?php echo htmlspecialchars($order['category_name']); ?></div>
                                </div>
                                
                                <div class="order-detail">
                                    <div class="order-detail-label">دورة الفوترة</div>
                                    <div class="order-detail-value">
                                        <?php
                                        $billingTexts = [
                                            'monthly' => 'شهرياً',
                                            'quarterly' => 'كل 3 أشهر',
                                            'semi_annual' => 'كل 6 أشهر',
                                            'annual' => 'سنوياً'
                                        ];
                                        echo $billingTexts[$order['billing_cycle']] ?? $order['billing_cycle'];
                                        ?>
                                    </div>
                                </div>
                                
                                <div class="order-detail">
                                    <div class="order-detail-label">المبلغ الإجمالي</div>
                                    <div class="order-detail-value">
                                        <?php echo number_format($order['total_amount'], 2); ?> <?php echo CURRENCY_SYMBOL; ?>
                                    </div>
                                </div>
                                
                                <?php if ($order['domain']): ?>
                                <div class="order-detail">
                                    <div class="order-detail-label">النطاق</div>
                                    <div class="order-detail-value"><?php echo htmlspecialchars($order['domain']); ?></div>
                                </div>
                                <?php endif; ?>
                                
                                <div class="order-detail">
                                    <div class="order-detail-label">تاريخ الطلب</div>
                                    <div class="order-detail-value">
                                        <?php echo date('Y/m/d H:i', strtotime($order['created_at'])); ?>
                                    </div>
                                </div>
                            </div>
                            
                            <?php if ($order['notes']): ?>
                                <div style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid #eee;">
                                    <strong>ملاحظات:</strong>
                                    <p style="margin-top: 0.5rem;"><?php echo nl2br(htmlspecialchars($order['notes'])); ?></p>
                                </div>
                            <?php endif; ?>
                            
                            <div class="order-actions" style="margin-top: 1rem;">
                                <a href="order-details.php?id=<?php echo $order['id']; ?>" class="btn btn-outline btn-sm">
                                    <i class="fas fa-eye"></i> عرض التفاصيل
                                </a>
                                
                                <?php if ($order['status'] === 'pending'): ?>
                                    <a href="../payment.php?order_id=<?php echo $order['id']; ?>" class="btn btn-success btn-sm">
                                        <i class="fas fa-credit-card"></i> دفع الآن
                                    </a>
                                <?php endif; ?>
                                
                                <?php if (in_array($order['status'], ['pending', 'processing'])): ?>
                                    <button class="btn btn-danger btn-sm" onclick="cancelOrder(<?php echo $order['id']; ?>)">
                                        <i class="fas fa-times"></i> إلغاء الطلب
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </main>
    </div>
    
    <script src="../assets/js/main.js"></script>
    <script>
        // تفعيل الشريط الجانبي المحمول
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            document.getElementById('sidebar').classList.toggle('active');
        });
        
        // إغلاق الشريط الجانبي عند النقر خارجه
        document.addEventListener('click', function(e) {
            const sidebar = document.getElementById('sidebar');
            const toggle = document.getElementById('sidebarToggle');
            
            if (!sidebar.contains(e.target) && !toggle.contains(e.target)) {
                sidebar.classList.remove('active');
            }
        });
        
        // إلغاء الطلب
        function cancelOrder(orderId) {
            if (confirm('هل أنت متأكد من إلغاء هذا الطلب؟')) {
                // هنا يمكن إضافة AJAX لإلغاء الطلب
                window.location.href = 'cancel-order.php?id=' + orderId;
            }
        }
    </script>
</body>
</html>
