# نقره هوست - إعدادات Apache
# Nakra Host - Apache Configuration

# تفعيل محرك إعادة الكتابة
RewriteEngine On

# منع الوصول للملفات الحساسة
<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "database.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files ".htaccess">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.sql">
    Order Allow,Deny
    Deny from all
</Files>

# منع عرض محتويات المجلدات
Options -Indexes

# حماية من الهجمات الشائعة
<IfModule mod_rewrite.c>
    # منع الوصول للملفات المخفية
    RewriteRule ^\.(.*)$ - [F,L]
    
    # منع الوصول لملفات النسخ الاحتياطية
    RewriteRule \.(bak|backup|old|tmp)$ - [F,L]
    
    # منع محاولات SQL Injection
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (SELECT|UNION|INSERT|DROP|DELETE|UPDATE|CREATE|ALTER) [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# ضغط الملفات لتحسين الأداء
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# تخزين مؤقت للملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# إعدادات الأمان الإضافية
<IfModule mod_headers.c>
    # منع تضمين الموقع في إطارات خارجية
    Header always append X-Frame-Options SAMEORIGIN
    
    # منع تشغيل السكريبت في المتصفح
    Header set X-XSS-Protection "1; mode=block"
    
    # منع تخمين نوع المحتوى
    Header set X-Content-Type-Options nosniff
    
    # إخفاء معلومات الخادم
    Header unset Server
    Header unset X-Powered-By
    
    # سياسة الأمان للمحتوى
    Header set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' fonts.googleapis.com cdnjs.cloudflare.com; font-src 'self' fonts.gstatic.com; img-src 'self' data:; connect-src 'self';"
</IfModule>

# إعادة توجيه للصفحات المفقودة
ErrorDocument 404 /404.php
ErrorDocument 403 /403.php
ErrorDocument 500 /500.php

# تحسين الأداء
<IfModule mod_mime.c>
    # ضغط JavaScript و CSS
    AddType text/css .css
    AddType application/javascript .js
    
    # تحديد ترميز UTF-8 للملفات النصية
    AddCharset utf-8 .html .css .js .xml .json .rss .atom
</IfModule>

# منع الوصول المباشر للملفات PHP في مجلدات معينة
<IfModule mod_rewrite.c>
    # منع تشغيل PHP في مجلد الرفع
    RewriteRule ^uploads/.*\.php$ - [F,L]
    
    # منع الوصول لملفات التكوين
    RewriteRule ^config/.*$ - [F,L]
    
    # منع الوصول لمجلد قاعدة البيانات
    RewriteRule ^database/.*$ - [F,L]
</IfModule>

# تحديد حد أقصى لحجم الملف المرفوع
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
php_value max_input_time 300

# تفعيل تسجيل الأخطاء (للتطوير فقط)
# php_flag display_errors Off
# php_flag log_errors On
# php_value error_log /path/to/error.log

# إعدادات الجلسة
php_value session.cookie_httponly 1
php_value session.cookie_secure 1
php_value session.use_strict_mode 1

# منع تشغيل السكريبت من مجلدات معينة
<Directory "uploads">
    php_flag engine off
</Directory>

# إعادة كتابة URL للصفحات الودية
<IfModule mod_rewrite.c>
    # إعادة توجيه www إلى non-www (اختياري)
    # RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
    # RewriteRule ^(.*)$ https://%1/$1 [R=301,L]
    
    # إجبار HTTPS (اختياري)
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    # صفحات ودية للخدمات
    RewriteRule ^service/([0-9]+)/?$ order.php?service_id=$1 [L,QSA]
    RewriteRule ^category/([0-9]+)/?$ services.php?category_id=$1 [L,QSA]
</IfModule>
