<?php
/**
 * ملف تثبيت نقره هوست
 * Nakra Host Installation Script
 */

// التحقق من وجود ملف التثبيت
if (file_exists('installed.lock')) {
    die('تم تثبيت الموقع بالفعل. إذا كنت تريد إعادة التثبيت، احذف ملف installed.lock');
}

$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$error = '';
$success = '';

// معالجة خطوات التثبيت
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($step) {
        case 1:
            // التحقق من المتطلبات
            $step = 2;
            break;
            
        case 2:
            // إعداد قاعدة البيانات
            $dbHost = $_POST['db_host'] ?? 'localhost';
            $dbName = $_POST['db_name'] ?? '';
            $dbUser = $_POST['db_user'] ?? '';
            $dbPass = $_POST['db_pass'] ?? '';
            
            if (empty($dbName) || empty($dbUser)) {
                $error = 'يرجى ملء جميع حقول قاعدة البيانات المطلوبة';
            } else {
                try {
                    // اختبار الاتصال
                    $pdo = new PDO("mysql:host=$dbHost", $dbUser, $dbPass);
                    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                    
                    // إنشاء قاعدة البيانات
                    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                    $pdo->exec("USE `$dbName`");
                    
                    // تشغيل ملف SQL
                    $sql = file_get_contents('database/schema.sql');
                    $sql = str_replace('nakra_host_db', $dbName, $sql);
                    
                    // تقسيم الاستعلامات
                    $queries = explode(';', $sql);
                    foreach ($queries as $query) {
                        $query = trim($query);
                        if (!empty($query)) {
                            $pdo->exec($query);
                        }
                    }
                    
                    // حفظ إعدادات قاعدة البيانات
                    $configContent = file_get_contents('config/database.php');
                    $configContent = str_replace('localhost', $dbHost, $configContent);
                    $configContent = str_replace('nakra_host_db', $dbName, $configContent);
                    $configContent = str_replace('nakra_user', $dbUser, $configContent);
                    $configContent = str_replace('secure_password_here', $dbPass, $configContent);
                    
                    file_put_contents('config/database.php', $configContent);
                    
                    $step = 3;
                    $success = 'تم إنشاء قاعدة البيانات بنجاح';
                    
                } catch (Exception $e) {
                    $error = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
                }
            }
            break;
            
        case 3:
            // إعداد المدير
            $adminUser = $_POST['admin_user'] ?? '';
            $adminEmail = $_POST['admin_email'] ?? '';
            $adminPass = $_POST['admin_pass'] ?? '';
            $adminName = $_POST['admin_name'] ?? '';
            
            if (empty($adminUser) || empty($adminEmail) || empty($adminPass) || empty($adminName)) {
                $error = 'يرجى ملء جميع حقول المدير';
            } else {
                try {
                    require_once 'config/database.php';
                    $db = Database::getInstance();
                    
                    // حذف المدير الافتراضي
                    $db->query("DELETE FROM users WHERE username = 'admin'");
                    
                    // إضافة المدير الجديد
                    $hashedPassword = password_hash($adminPass, PASSWORD_DEFAULT);
                    $nameParts = explode(' ', $adminName, 2);
                    $firstName = $nameParts[0];
                    $lastName = $nameParts[1] ?? '';
                    
                    $db->query("
                        INSERT INTO users (username, email, password, first_name, last_name, role, status, email_verified) 
                        VALUES (?, ?, ?, ?, ?, 'admin', 'active', 1)
                    ", [$adminUser, $adminEmail, $hashedPassword, $firstName, $lastName]);
                    
                    $step = 4;
                    $success = 'تم إنشاء حساب المدير بنجاح';
                    
                } catch (Exception $e) {
                    $error = 'خطأ في إنشاء حساب المدير: ' . $e->getMessage();
                }
            }
            break;
            
        case 4:
            // إنهاء التثبيت
            file_put_contents('installed.lock', date('Y-m-d H:i:s'));
            $success = 'تم تثبيت الموقع بنجاح!';
            break;
    }
}

// فحص المتطلبات
function checkRequirements() {
    $requirements = [
        'PHP Version >= 7.4' => version_compare(PHP_VERSION, '7.4.0', '>='),
        'PDO Extension' => extension_loaded('pdo'),
        'PDO MySQL Extension' => extension_loaded('pdo_mysql'),
        'mbstring Extension' => extension_loaded('mbstring'),
        'Config Directory Writable' => is_writable('config/'),
        'Database Directory Readable' => is_readable('database/'),
    ];
    
    return $requirements;
}

$requirements = checkRequirements();
$allRequirementsMet = !in_array(false, $requirements);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نقره هوست</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c3e50, #3498db);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }
        
        .install-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            width: 100%;
            max-width: 600px;
            margin: 20px;
        }
        
        .install-header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 2rem;
            border-radius: 10px 10px 0 0;
            text-align: center;
        }
        
        .install-body {
            padding: 2rem;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #ecf0f1;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
            position: relative;
        }
        
        .step.active {
            background: #3498db;
            color: white;
        }
        
        .step.completed {
            background: #27ae60;
            color: white;
        }
        
        .step:not(:last-child)::after {
            content: '';
            position: absolute;
            left: -20px;
            width: 20px;
            height: 2px;
            background: #ecf0f1;
            top: 50%;
            transform: translateY(-50%);
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #bdc3c7;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #3498db;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        
        .alert-danger {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .requirements-list {
            list-style: none;
        }
        
        .requirements-list li {
            padding: 0.5rem 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .status-ok {
            color: #27ae60;
            font-weight: bold;
        }
        
        .status-error {
            color: #e74c3c;
            font-weight: bold;
        }
        
        .text-center {
            text-align: center;
        }
        
        .mt-2 {
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <h1>🚀 تثبيت نقره هوست</h1>
            <p>مرحباً بك في معالج تثبيت موقع نقره هوست</p>
        </div>
        
        <div class="install-body">
            <!-- مؤشر الخطوات -->
            <div class="step-indicator">
                <div class="step <?php echo $step >= 1 ? ($step > 1 ? 'completed' : 'active') : ''; ?>">1</div>
                <div class="step <?php echo $step >= 2 ? ($step > 2 ? 'completed' : 'active') : ''; ?>">2</div>
                <div class="step <?php echo $step >= 3 ? ($step > 3 ? 'completed' : 'active') : ''; ?>">3</div>
                <div class="step <?php echo $step >= 4 ? 'active' : ''; ?>">4</div>
            </div>
            
            <!-- عرض الرسائل -->
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    ❌ <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    ✅ <?php echo $success; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($step === 1): ?>
                <!-- الخطوة 1: فحص المتطلبات -->
                <h2>الخطوة 1: فحص المتطلبات</h2>
                <p>يرجى التأكد من توفر جميع المتطلبات التالية:</p>
                
                <ul class="requirements-list">
                    <?php foreach ($requirements as $requirement => $status): ?>
                        <li>
                            <span><?php echo $requirement; ?></span>
                            <span class="<?php echo $status ? 'status-ok' : 'status-error'; ?>">
                                <?php echo $status ? '✅ متوفر' : '❌ غير متوفر'; ?>
                            </span>
                        </li>
                    <?php endforeach; ?>
                </ul>
                
                <div class="text-center mt-2">
                    <?php if ($allRequirementsMet): ?>
                        <form method="POST">
                            <button type="submit" class="btn btn-primary">متابعة للخطوة التالية</button>
                        </form>
                    <?php else: ?>
                        <p style="color: #e74c3c;">يرجى حل المشاكل أعلاه قبل المتابعة</p>
                        <a href="install.php" class="btn btn-primary">إعادة فحص</a>
                    <?php endif; ?>
                </div>
                
            <?php elseif ($step === 2): ?>
                <!-- الخطوة 2: إعداد قاعدة البيانات -->
                <h2>الخطوة 2: إعداد قاعدة البيانات</h2>
                <p>أدخل معلومات قاعدة البيانات:</p>
                
                <form method="POST">
                    <div class="form-group">
                        <label for="db_host" class="form-label">خادم قاعدة البيانات</label>
                        <input type="text" id="db_host" name="db_host" class="form-control" value="localhost" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="db_name" class="form-label">اسم قاعدة البيانات</label>
                        <input type="text" id="db_name" name="db_name" class="form-control" placeholder="nakra_host_db" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="db_user" class="form-label">اسم المستخدم</label>
                        <input type="text" id="db_user" name="db_user" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="db_pass" class="form-label">كلمة المرور</label>
                        <input type="password" id="db_pass" name="db_pass" class="form-control">
                    </div>
                    
                    <div class="text-center">
                        <button type="submit" class="btn btn-primary">إنشاء قاعدة البيانات</button>
                    </div>
                </form>
                
            <?php elseif ($step === 3): ?>
                <!-- الخطوة 3: إعداد المدير -->
                <h2>الخطوة 3: إنشاء حساب المدير</h2>
                <p>أنشئ حساب المدير الرئيسي:</p>
                
                <form method="POST">
                    <div class="form-group">
                        <label for="admin_name" class="form-label">الاسم الكامل</label>
                        <input type="text" id="admin_name" name="admin_name" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="admin_user" class="form-label">اسم المستخدم</label>
                        <input type="text" id="admin_user" name="admin_user" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="admin_email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" id="admin_email" name="admin_email" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="admin_pass" class="form-label">كلمة المرور</label>
                        <input type="password" id="admin_pass" name="admin_pass" class="form-control" required>
                    </div>
                    
                    <div class="text-center">
                        <button type="submit" class="btn btn-primary">إنشاء حساب المدير</button>
                    </div>
                </form>
                
            <?php elseif ($step === 4): ?>
                <!-- الخطوة 4: اكتمال التثبيت -->
                <div class="text-center">
                    <h2>🎉 تم التثبيت بنجاح!</h2>
                    <p>تم تثبيت موقع نقره هوست بنجاح. يمكنك الآن البدء في استخدام الموقع.</p>
                    
                    <div class="mt-2">
                        <a href="index.php" class="btn btn-success">زيارة الموقع</a>
                        <a href="auth/login.php" class="btn btn-primary">تسجيل الدخول</a>
                    </div>
                    
                    <div class="mt-2">
                        <p><strong>ملاحظة:</strong> لأسباب أمنية، يرجى حذف ملف install.php من الخادم.</p>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
