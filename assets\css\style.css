/* نقره هوست - التصميم الاحترافي المتقدم */
/* Nakra Host - Professional Advanced Stylesheet */

/* استيراد الخطوط العربية الاحترافية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');

/* المتغيرات المتقدمة */
:root {
    /* الألوان الأساسية */
    --primary-color: #1a1a2e;
    --primary-light: #16213e;
    --primary-dark: #0f0f1e;
    --secondary-color: #0066ff;
    --secondary-light: #3385ff;
    --secondary-dark: #0052cc;
    --accent-color: #ff6b35;
    --accent-light: #ff8c69;
    --accent-dark: #e55a2b;

    /* ألوان الحالة */
    --success-color: #00d4aa;
    --warning-color: #ffb800;
    --danger-color: #ff4757;
    --info-color: #3742fa;

    /* ألوان الخلفية */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-dark: #1a1a2e;
    --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --bg-gradient-2: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --bg-gradient-3: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);

    /* النصوص */
    --text-primary: #2d3748;
    --text-secondary: #718096;
    --text-light: #a0aec0;
    --text-white: #ffffff;

    /* الحدود والظلال */
    --border-color: #e2e8f0;
    --border-light: #f7fafc;
    --shadow-sm: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.07), 0 2px 4px rgba(0,0,0,0.06);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.1), 0 4px 6px rgba(0,0,0,0.05);
    --shadow-xl: 0 20px 25px rgba(0,0,0,0.15), 0 10px 10px rgba(0,0,0,0.04);
    --shadow-2xl: 0 25px 50px rgba(0,0,0,0.25);
    --shadow-glow: 0 0 20px rgba(102, 126, 234, 0.4);

    /* الانتقالات والحركة */
    --transition-fast: all 0.15s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;
    --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* الأبعاد */
    --border-radius-sm: 6px;
    --border-radius: 12px;
    --border-radius-lg: 20px;
    --border-radius-xl: 30px;
    --border-radius-full: 50px;

    /* المسافات */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
}

/* إعدادات عامة متقدمة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

html {
    direction: rtl;
    scroll-behavior: smooth;
    font-size: 16px;
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: 'Cairo', 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.7;
    color: var(--text-primary);
    background: var(--bg-secondary);
    overflow-x: hidden;
    position: relative;
}

/* تأثيرات الخلفية المتحركة */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    z-index: -1;
    animation: backgroundFloat 20s ease-in-out infinite;
}

/* الحركات الأساسية */
@keyframes backgroundFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-30px) rotate(1deg); }
    66% { transform: translateY(30px) rotate(-1deg); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 20px rgba(102, 126, 234, 0.4); }
    50% { box-shadow: 0 0 40px rgba(102, 126, 234, 0.8); }
}

@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-100px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromBottom {
    from {
        opacity: 0;
        transform: translateY(100px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* فئات الحركة */
.animate-fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
}

.animate-fade-in-down {
    animation: fadeInDown 0.8s ease-out forwards;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.8s ease-out forwards;
}

.animate-fade-in-right {
    animation: fadeInRight 0.8s ease-out forwards;
}

.animate-scale-in {
    animation: scaleIn 0.6s ease-out forwards;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-glow {
    animation: glow 2s ease-in-out infinite;
}

.animate-slide-in-top {
    animation: slideInFromTop 0.8s ease-out forwards;
}

.animate-slide-in-bottom {
    animation: slideInFromBottom 0.8s ease-out forwards;
}

/* الحاويات */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.container-fluid {
    width: 100%;
    padding: 0 20px;
}

/* الشبكة */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col {
    flex: 1;
    padding: 0 15px;
}

.col-1 { flex: 0 0 8.333333%; }
.col-2 { flex: 0 0 16.666667%; }
.col-3 { flex: 0 0 25%; }
.col-4 { flex: 0 0 33.333333%; }
.col-6 { flex: 0 0 50%; }
.col-8 { flex: 0 0 66.666667%; }
.col-9 { flex: 0 0 75%; }
.col-12 { flex: 0 0 100%; }

/* الرأس الاحترافي */
.header {
    background: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: white;
    padding: 1rem 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: var(--transition-normal);
    animation: slideInFromTop 0.8s ease-out;
}

.header.scrolled {
    background: rgba(26, 26, 46, 0.98);
    box-shadow: var(--shadow-xl);
    transform: translateY(0);
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.logo {
    font-size: 2rem;
    font-weight: 800;
    text-decoration: none;
    color: white;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition-bounce);
    position: relative;
}

.logo::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(45deg, var(--secondary-color), var(--accent-color));
    border-radius: var(--border-radius);
    opacity: 0;
    z-index: -1;
    transition: var(--transition-normal);
}

.logo:hover::before {
    opacity: 0.2;
}

.logo:hover {
    transform: scale(1.05);
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.logo i {
    background: linear-gradient(45deg, var(--secondary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: pulse 2s infinite;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 0.5rem;
    align-items: center;
}

.nav-menu li {
    position: relative;
}

.nav-menu a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    padding: 0.75rem 1.25rem;
    border-radius: var(--border-radius-full);
    transition: var(--transition-bounce);
    position: relative;
    overflow: hidden;
    display: block;
}

.nav-menu a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-normal);
}

.nav-menu a:hover::before {
    left: 100%;
}

.nav-menu a:hover,
.nav-menu a.active {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow);
}

.nav-menu a:active {
    transform: translateY(0);
}

/* القائمة المحمولة */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
}

/* قسم البطل الاحترافي */
.hero {
    min-height: 100vh;
    background: linear-gradient(135deg,
        rgba(26, 26, 46, 0.9) 0%,
        rgba(22, 33, 62, 0.9) 50%,
        rgba(26, 26, 46, 0.9) 100%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23667eea" stop-opacity="0.3"/><stop offset="100%" stop-color="%23764ba2" stop-opacity="0.1"/></radialGradient></defs><rect width="100%" height="100%" fill="url(%23a)"/></svg>');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    color: white;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding-top: 80px;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255, 107, 53, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(0, 212, 170, 0.2) 0%, transparent 50%);
    animation: backgroundFloat 15s ease-in-out infinite;
}

.hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"><animate attributeName="opacity" values="0;1;0" dur="3s" repeatCount="indefinite"/></circle><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"><animate attributeName="opacity" values="0;1;0" dur="4s" repeatCount="indefinite"/></circle><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"><animate attributeName="opacity" values="0;1;0" dur="5s" repeatCount="indefinite"/></circle></svg>');
    background-size: 200px 200px;
    animation: float 20s linear infinite;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 2rem;
}

.hero h1 {
    font-size: clamp(2.5rem, 5vw, 4.5rem);
    font-weight: 900;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #667eea 50%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
    animation: fadeInUp 1s ease-out 0.2s both;
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.3);
}

.hero .subtitle {
    font-size: clamp(1.1rem, 2.5vw, 1.4rem);
    margin-bottom: 1rem;
    opacity: 0.95;
    font-weight: 500;
    animation: fadeInUp 1s ease-out 0.4s both;
    color: rgba(255, 255, 255, 0.9);
}

.hero .description {
    font-size: clamp(1rem, 2vw, 1.2rem);
    margin-bottom: 3rem;
    opacity: 0.8;
    font-weight: 400;
    line-height: 1.6;
    animation: fadeInUp 1s ease-out 0.6s both;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease-out 0.8s both;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 4rem;
    animation: fadeInUp 1s ease-out 1s both;
}

.hero-stat {
    text-align: center;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition-normal);
}

.hero-stat:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: var(--shadow-xl);
}

.hero-stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--accent-color);
    display: block;
    margin-bottom: 0.5rem;
}

.hero-stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    font-weight: 500;
}

/* تأثيرات الجسيمات المتحركة */
.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.particle {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: particleFloat 15s linear infinite;
}

.particle:nth-child(1) { left: 10%; animation-delay: 0s; width: 4px; height: 4px; }
.particle:nth-child(2) { left: 20%; animation-delay: 2s; width: 6px; height: 6px; }
.particle:nth-child(3) { left: 30%; animation-delay: 4s; width: 3px; height: 3px; }
.particle:nth-child(4) { left: 40%; animation-delay: 6s; width: 5px; height: 5px; }
.particle:nth-child(5) { left: 50%; animation-delay: 8s; width: 4px; height: 4px; }
.particle:nth-child(6) { left: 60%; animation-delay: 10s; width: 6px; height: 6px; }
.particle:nth-child(7) { left: 70%; animation-delay: 12s; width: 3px; height: 3px; }
.particle:nth-child(8) { left: 80%; animation-delay: 14s; width: 5px; height: 5px; }
.particle:nth-child(9) { left: 90%; animation-delay: 16s; width: 4px; height: 4px; }

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

/* الأزرار الاحترافية */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border: none;
    border-radius: var(--border-radius-full);
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: var(--transition-bounce);
    font-size: 1rem;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    min-height: 50px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-normal);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--secondary-dark), var(--secondary-color));
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

.btn-secondary {
    background: linear-gradient(135deg, var(--accent-color), var(--accent-dark));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, var(--accent-dark), var(--accent-color));
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #00b894);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-success:hover {
    background: linear-gradient(135deg, #00b894, var(--success-color));
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--secondary-color);
    color: var(--secondary-color);
    position: relative;
}

.btn-outline::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
    transition: var(--transition-normal);
    z-index: -1;
}

.btn-outline:hover {
    color: white;
    border-color: var(--secondary-dark);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.btn-outline:hover::after {
    width: 100%;
}

.btn-lg {
    padding: 1.25rem 2.5rem;
    font-size: 1.1rem;
    min-height: 60px;
}

.btn-sm {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
    min-height: 40px;
}

.btn-block {
    width: 100%;
}

.btn:active {
    transform: translateY(-1px);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn:disabled:hover {
    transform: none;
    box-shadow: var(--shadow-md);
}

/* البطاقات */
.card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 2rem;
    margin-bottom: 2rem;
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.card-header {
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 1rem;
    margin-bottom: 1rem;
}

.card-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

/* الأقسام الاحترافية */
.section {
    padding: 6rem 0;
    position: relative;
}

.section-header {
    margin-bottom: 4rem;
}

.section-title {
    font-size: clamp(2rem, 4vw, 3.5rem);
    font-weight: 800;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.section-subtitle {
    font-size: clamp(1rem, 2vw, 1.3rem);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* قسم خطط الاستضافة */
.hosting-plans-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
    overflow: hidden;
}

.hosting-plans-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="10" cy="10" r="1" fill="rgba(102,126,234,0.1)"/><circle cx="90" cy="20" r="1.5" fill="rgba(255,107,53,0.1)"/><circle cx="20" cy="90" r="1" fill="rgba(0,212,170,0.1)"/><circle cx="80" cy="80" r="1.2" fill="rgba(102,126,234,0.1)"/></svg>');
    background-size: 100px 100px;
    animation: float 30s linear infinite;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.pricing-card {
    background: white;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    position: relative;
    transition: var(--transition-bounce);
    border: 1px solid var(--border-light);
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-2xl);
}

.pricing-card.popular {
    transform: scale(1.05);
    border: 2px solid var(--secondary-color);
    box-shadow: var(--shadow-xl);
}

.pricing-card.popular:hover {
    transform: scale(1.05) translateY(-10px);
}

.popular-badge {
    position: absolute;
    top: 20px;
    right: -30px;
    background: linear-gradient(135deg, var(--accent-color), var(--accent-dark));
    color: white;
    padding: 0.5rem 2rem;
    font-size: 0.8rem;
    font-weight: 700;
    transform: rotate(45deg);
    z-index: 10;
    box-shadow: var(--shadow-md);
}

.pricing-header {
    padding: 2rem;
    text-align: center;
    background: linear-gradient(135deg, var(--bg-secondary), white);
    border-bottom: 1px solid var(--border-light);
}

.pricing-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1rem;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    box-shadow: var(--shadow-lg);
    animation: float 3s ease-in-out infinite;
}

.pricing-card.basic .pricing-icon {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
}

.pricing-card.advanced .pricing-icon {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    animation: glow 2s ease-in-out infinite;
}

.pricing-card.professional .pricing-icon {
    background: linear-gradient(135deg, #fdcb6e, #e17055);
}

.pricing-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.pricing-header p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.pricing-price {
    padding: 1.5rem 2rem;
    text-align: center;
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-light);
}

.pricing-price .currency {
    font-size: 1.2rem;
    color: var(--text-secondary);
    vertical-align: top;
}

.pricing-price .amount {
    font-size: 3rem;
    font-weight: 900;
    color: var(--primary-color);
    margin: 0 0.25rem;
}

.pricing-price .period {
    font-size: 1rem;
    color: var(--text-secondary);
}

.pricing-features {
    padding: 2rem;
}

.pricing-features ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.pricing-features li {
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.95rem;
    transition: var(--transition-normal);
}

.pricing-features li:last-child {
    border-bottom: none;
}

.pricing-features li:hover {
    background: var(--bg-secondary);
    margin: 0 -2rem;
    padding-left: 2rem;
    padding-right: 2rem;
}

.pricing-features li i {
    color: var(--success-color);
    font-size: 1rem;
    width: 20px;
    text-align: center;
}

.pricing-footer {
    padding: 2rem;
    background: var(--bg-secondary);
}

/* مميزات الاستضافة */
.hosting-features {
    margin-top: 4rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-item {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: var(--transition-bounce);
    border: 1px solid var(--border-light);
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    width: 70px;
    height: 70px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: white;
    box-shadow: var(--shadow-lg);
}

.feature-item h4 {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.feature-item p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* الخدمات الحديثة */
.services-section {
    background: var(--bg-primary);
    position: relative;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: 2.5rem;
}

.service-card.modern {
    background: white;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    transition: var(--transition-bounce);
    border: 1px solid var(--border-light);
    position: relative;
}

.service-card.modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
}

.service-card.modern:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-2xl);
}

.service-card.modern:hover .service-icon {
    transform: scale(1.1) rotate(5deg);
}

.service-icon {
    width: 100px;
    height: 100px;
    margin: 2rem auto 1.5rem;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    color: white;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-bounce);
    position: relative;
}

.service-icon::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border-radius: 50%;
    opacity: 0.2;
    z-index: -1;
    animation: pulse 3s infinite;
}

.service-content {
    padding: 0 2rem 2rem;
    text-align: center;
}

.service-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.service-content p {
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.service-features {
    list-style: none;
    margin: 0 0 2rem 0;
    padding: 0;
}

.service-features li {
    padding: 0.5rem 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
    position: relative;
    padding-right: 1.5rem;
}

.service-features li::before {
    content: '✓';
    position: absolute;
    right: 0;
    color: var(--success-color);
    font-weight: bold;
}

.service-footer {
    padding: 0 2rem 2rem;
    text-align: center;
}

/* تخصيص ألوان الخدمات */
.service-card.modern:nth-child(1) .service-icon {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
}

.service-card.modern:nth-child(2) .service-icon {
    background: linear-gradient(135deg, #00b894, #00a085);
}

.service-card.modern:nth-child(3) .service-icon {
    background: linear-gradient(135deg, #fdcb6e, #e17055);
}

.service-card.modern:nth-child(1) .service-icon::before {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
}

.service-card.modern:nth-child(2) .service-icon::before {
    background: linear-gradient(135deg, #00b894, #00a085);
}

.service-card.modern:nth-child(3) .service-icon::before {
    background: linear-gradient(135deg, #fdcb6e, #e17055);
}

/* النماذج */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-color);
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    font-family: inherit;
}

.form-control:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* الجداول */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1rem;
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.table th,
.table td {
    padding: 12px;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    background-color: var(--light-color);
    font-weight: 600;
    color: var(--primary-color);
}

/* التذييل */
.footer {
    background-color: var(--primary-color);
    color: white;
    padding: 3rem 0 1rem;
    margin-top: 4rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    margin-bottom: 1rem;
    color: var(--secondary-color);
}

.footer-section a {
    color: white;
    text-decoration: none;
    transition: var(--transition);
}

.footer-section a:hover {
    color: var(--secondary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255,255,255,0.1);
    opacity: 0.8;
}

/* الرسائل */
.alert {
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    border: 1px solid transparent;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

/* التصميم المتجاوب المتقدم */

/* الأجهزة الكبيرة جداً */
@media (min-width: 1400px) {
    .container {
        max-width: 1320px;
    }

    .hero h1 {
        font-size: 5rem;
    }

    .pricing-grid {
        grid-template-columns: repeat(3, 1fr);
        max-width: 1200px;
        margin: 0 auto;
    }
}

/* الأجهزة المتوسطة */
@media (max-width: 1024px) {
    .pricing-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .pricing-card.popular {
        transform: none;
    }

    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
}

/* الأجهزة اللوحية */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
        background: none;
        border: none;
        color: white;
        font-size: 1.5rem;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: var(--border-radius);
        transition: var(--transition-normal);
    }

    .mobile-menu-toggle:hover {
        background: rgba(255, 255, 255, 0.1);
    }

    .nav-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: rgba(26, 26, 46, 0.98);
        backdrop-filter: blur(20px);
        flex-direction: column;
        padding: 2rem 1rem;
        box-shadow: var(--shadow-xl);
        border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
        gap: 0;
    }

    .nav-menu.active {
        display: flex;
        animation: slideInFromTop 0.3s ease-out;
    }

    .nav-menu li {
        width: 100%;
    }

    .nav-menu a {
        width: 100%;
        text-align: center;
        padding: 1rem;
        margin-bottom: 0.5rem;
        border-radius: var(--border-radius);
    }

    .hero {
        min-height: 80vh;
        padding-top: 100px;
    }

    .hero-content {
        padding: 0 1rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .hero-buttons .btn {
        width: 100%;
        max-width: 300px;
    }

    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .pricing-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .pricing-card {
        max-width: 400px;
        margin: 0 auto;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .section {
        padding: 4rem 0;
    }

    .section-header {
        margin-bottom: 3rem;
    }
}

/* الهواتف المحمولة */
@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }

    .hero {
        min-height: 70vh;
        padding-top: 80px;
    }

    .hero-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .hero-stat {
        padding: 1rem;
    }

    .hero-stat-number {
        font-size: 2rem;
    }

    .pricing-card {
        margin: 0;
    }

    .pricing-header {
        padding: 1.5rem;
    }

    .pricing-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .pricing-price .amount {
        font-size: 2.5rem;
    }

    .pricing-features {
        padding: 1.5rem;
    }

    .pricing-footer {
        padding: 1.5rem;
    }

    .service-icon {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }

    .service-content {
        padding: 0 1.5rem 1.5rem;
    }

    .service-footer {
        padding: 0 1.5rem 1.5rem;
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .feature-item {
        padding: 1.5rem;
    }

    .btn {
        padding: 0.875rem 1.5rem;
        font-size: 0.9rem;
    }

    .btn-lg {
        padding: 1rem 2rem;
        font-size: 1rem;
    }
}

/* تحسينات الأداء للأجهزة الضعيفة */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .hero::before,
    .hero::after {
        animation: none;
    }

    .particle {
        display: none;
    }
}

/* تحسينات للطباعة */
@media print {
    .header,
    .footer,
    .hero-particles,
    .mobile-menu-toggle {
        display: none !important;
    }

    .hero {
        background: white !important;
        color: black !important;
        min-height: auto !important;
    }

    .section {
        padding: 2rem 0 !important;
    }

    .pricing-card,
    .service-card,
    .feature-item {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #ccc !important;
    }
}

/* قسم الإحصائيات المتقدمة */
.stats-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"><animate attributeName="opacity" values="0;1;0" dur="4s" repeatCount="indefinite"/></circle><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"><animate attributeName="opacity" values="0;1;0" dur="3s" repeatCount="indefinite"/></circle><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"><animate attributeName="opacity" values="0;1;0" dur="5s" repeatCount="indefinite"/></circle></svg>');
    background-size: 150px 150px;
    animation: float 25s linear infinite;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    position: relative;
    z-index: 2;
}

.stat-item {
    text-align: center;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition-bounce);
    position: relative;
    overflow: hidden;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: var(--transition-normal);
}

.stat-item:hover::before {
    left: 100%;
}

.stat-item:hover {
    transform: translateY(-10px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: var(--shadow-2xl);
}

.stat-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, var(--accent-color), var(--accent-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    box-shadow: var(--shadow-lg);
    animation: float 3s ease-in-out infinite;
}

.stat-number {
    font-size: 3.5rem;
    font-weight: 900;
    color: white;
    display: block;
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stat-suffix {
    font-size: 2rem;
    font-weight: 700;
    color: var(--accent-light);
    display: inline;
}

.stat-label {
    font-size: 1.2rem;
    font-weight: 600;
    color: white;
    margin-bottom: 0.5rem;
}

.stat-description {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
}

/* قسم شهادات العملاء */
.testimonials-section {
    background: var(--bg-secondary);
    position: relative;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.testimonial-card {
    background: white;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
    padding: 2rem;
    transition: var(--transition-bounce);
    border: 1px solid var(--border-light);
    position: relative;
    overflow: hidden;
}

.testimonial-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
}

.testimonial-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-2xl);
}

.testimonial-content {
    margin-bottom: 2rem;
}

.testimonial-quote {
    text-align: right;
    margin-bottom: 1rem;
}

.testimonial-quote i {
    font-size: 2rem;
    color: var(--secondary-color);
    opacity: 0.3;
}

.testimonial-content p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-primary);
    font-style: italic;
    position: relative;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.author-avatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.author-info h4 {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.author-info span {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.testimonial-rating {
    display: flex;
    gap: 0.25rem;
}

.testimonial-rating i {
    color: #ffd700;
    font-size: 1.1rem;
}

/* قسم المميزات المتقدمة */
.features-advanced-section {
    background: var(--bg-primary);
    position: relative;
}

.features-advanced-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 3rem;
}

.feature-advanced-item {
    display: flex;
    align-items: flex-start;
    gap: 2rem;
    padding: 2rem;
    background: white;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
    transition: var(--transition-bounce);
    border: 1px solid var(--border-light);
}

.feature-advanced-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-2xl);
}

.feature-advanced-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    flex-shrink: 0;
    box-shadow: var(--shadow-lg);
}

.feature-advanced-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.feature-advanced-content p {
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.feature-advanced-content ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.feature-advanced-content li {
    padding: 0.5rem 0;
    color: var(--text-secondary);
    position: relative;
    padding-right: 1.5rem;
    font-size: 0.95rem;
}

.feature-advanced-content li::before {
    content: '✓';
    position: absolute;
    right: 0;
    color: var(--success-color);
    font-weight: bold;
    font-size: 1.1rem;
}

/* قسم العروض الخاصة */
.special-offers-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.special-offers-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><polygon points="0,0 100,0 80,100 0,80" fill="rgba(255,255,255,0.05)"/><polygon points="20,0 100,20 100,100 0,100" fill="rgba(255,255,255,0.03)"/></svg>');
    background-size: 200px 200px;
    animation: float 20s linear infinite;
}

.offers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    position: relative;
    z-index: 2;
}

.offer-card {
    background: white;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-2xl);
    overflow: hidden;
    position: relative;
    transition: var(--transition-bounce);
    color: var(--text-primary);
}

.offer-card:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: 0 30px 60px rgba(0,0,0,0.3);
}

.offer-card.limited-time {
    border: 3px solid var(--accent-color);
    animation: glow 2s ease-in-out infinite;
}

.offer-badge {
    position: absolute;
    top: 20px;
    right: -30px;
    background: linear-gradient(135deg, var(--accent-color), var(--accent-dark));
    color: white;
    padding: 0.5rem 2.5rem;
    font-size: 0.8rem;
    font-weight: 700;
    transform: rotate(45deg);
    z-index: 10;
    box-shadow: var(--shadow-lg);
    text-transform: uppercase;
}

.offer-header {
    padding: 2rem;
    text-align: center;
    background: linear-gradient(135deg, var(--bg-secondary), white);
    border-bottom: 1px solid var(--border-light);
}

.offer-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1rem;
    background: linear-gradient(135deg, var(--accent-color), var(--accent-dark));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    box-shadow: var(--shadow-lg);
    animation: pulse 2s infinite;
}

.offer-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.offer-header p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.offer-content {
    padding: 2rem;
}

.offer-price {
    text-align: center;
    margin-bottom: 2rem;
}

.offer-price .old-price {
    font-size: 1.2rem;
    color: var(--text-light);
    text-decoration: line-through;
    display: block;
    margin-bottom: 0.5rem;
}

.offer-price .new-price {
    font-size: 3rem;
    font-weight: 900;
    color: var(--accent-color);
    display: inline-block;
    margin-left: 0.5rem;
}

.offer-price .period {
    font-size: 1rem;
    color: var(--text-secondary);
}

.offer-features {
    list-style: none;
    margin: 0 0 2rem 0;
    padding: 0;
}

.offer-features li {
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.95rem;
}

.offer-features li:last-child {
    border-bottom: none;
}

.offer-features li::before {
    content: '✓';
    color: var(--success-color);
    font-weight: bold;
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.offer-timer {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
}

.timer-item {
    text-align: center;
    min-width: 60px;
}

.timer-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent-color);
}

.timer-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    text-transform: uppercase;
}

.offer-savings {
    text-align: center;
    padding: 1rem;
    background: linear-gradient(135deg, var(--success-color), #00b894);
    color: white;
    border-radius: var(--border-radius);
    font-weight: 600;
    margin-bottom: 2rem;
}

.offer-footer {
    padding: 0 2rem 2rem;
}

/* قسم الشراكات */
.partnerships-section {
    background: var(--bg-secondary);
    position: relative;
}

.partnerships-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.partnership-card {
    background: white;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    transition: var(--transition-bounce);
    border: 1px solid var(--border-light);
    position: relative;
}

.partnership-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
}

.partnership-card.main-partner {
    border: 2px solid var(--secondary-color);
    transform: scale(1.05);
}

.partnership-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-2xl);
}

.partnership-card.main-partner:hover {
    transform: scale(1.05) translateY(-10px);
}

.partnership-header {
    padding: 2rem;
    text-align: center;
    background: linear-gradient(135deg, var(--bg-secondary), white);
    border-bottom: 1px solid var(--border-light);
}

.partnership-logo {
    width: 80px;
    height: 80px;
    margin: 0 auto 1rem;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    box-shadow: var(--shadow-lg);
}

.partnership-card.main-partner .partnership-logo {
    background: linear-gradient(135deg, var(--accent-color), var(--accent-dark));
    animation: glow 3s ease-in-out infinite;
}

.partnership-header h3 {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.partnership-type {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background: var(--secondary-color);
    color: white;
    border-radius: var(--border-radius-full);
    font-size: 0.8rem;
    font-weight: 600;
}

.partnership-content {
    padding: 2rem;
}

.partnership-content p {
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.partnership-benefits {
    list-style: none;
    margin: 0;
    padding: 0;
}

.partnership-benefits li {
    padding: 0.5rem 0;
    color: var(--text-secondary);
    position: relative;
    padding-right: 1.5rem;
    font-size: 0.9rem;
}

.partnership-benefits li::before {
    content: '✓';
    position: absolute;
    right: 0;
    color: var(--success-color);
    font-weight: bold;
}

.partnership-footer {
    padding: 0 2rem 2rem;
    text-align: center;
}

/* قسم الأسئلة الشائعة */
.faq-section {
    background: var(--bg-primary);
    position: relative;
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    margin-bottom: 1rem;
    overflow: hidden;
    transition: var(--transition-normal);
    border: 1px solid var(--border-light);
}

.faq-item:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.faq-question {
    padding: 1.5rem 2rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, var(--bg-secondary), white);
    transition: var(--transition-normal);
    position: relative;
}

.faq-question::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    transform: scaleY(0);
    transition: var(--transition-normal);
}

.faq-question:hover::before {
    transform: scaleY(1);
}

.faq-question h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
    flex: 1;
}

.faq-question i {
    font-size: 1rem;
    color: var(--secondary-color);
    transition: var(--transition-normal);
    margin-right: 1rem;
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 2rem;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    background: white;
}

.faq-item.active .faq-answer {
    padding: 1.5rem 2rem;
    max-height: 200px;
}

.faq-answer p {
    color: var(--text-secondary);
    line-height: 1.7;
    margin: 0;
}

.faq-footer {
    margin-top: 3rem;
    padding: 2rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
}

.faq-footer p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

/* قسم الاتصال السريع */
.quick-contact-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.quick-contact-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="10" cy="10" r="2" fill="rgba(255,255,255,0.1)"><animate attributeName="opacity" values="0;1;0" dur="3s" repeatCount="indefinite"/></circle><circle cx="90" cy="30" r="1.5" fill="rgba(255,255,255,0.1)"><animate attributeName="opacity" values="0;1;0" dur="4s" repeatCount="indefinite"/></circle><circle cx="30" cy="90" r="1" fill="rgba(255,255,255,0.1)"><animate attributeName="opacity" values="0;1;0" dur="5s" repeatCount="indefinite"/></circle></svg>');
    background-size: 100px 100px;
    animation: float 20s linear infinite;
}

.quick-contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
    position: relative;
    z-index: 2;
}

.quick-contact-text h2 {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.quick-contact-text p {
    font-size: 1.1rem;
    line-height: 1.7;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.quick-contact-features {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.quick-feature {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.95rem;
    opacity: 0.9;
}

.quick-feature i {
    color: var(--accent-color);
    font-size: 1.2rem;
}

.quick-contact-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

.quick-contact-actions .btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
}

.quick-contact-actions .btn-success {
    background: linear-gradient(135deg, #25d366, #128c7e);
}

.quick-contact-actions .btn-success:hover {
    background: linear-gradient(135deg, #128c7e, #25d366);
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

/* تحسينات التصميم المتجاوب للأقسام الجديدة */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .stat-item {
        padding: 1.5rem;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .testimonial-card {
        padding: 1.5rem;
    }

    .features-advanced-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .feature-advanced-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .offers-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .offer-card {
        max-width: 400px;
        margin: 0 auto;
    }

    .partnerships-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .partnership-card.main-partner {
        transform: none;
    }

    .faq-question {
        padding: 1rem 1.5rem;
    }

    .faq-question h3 {
        font-size: 1rem;
    }

    .faq-item.active .faq-answer {
        padding: 1rem 1.5rem;
    }

    .quick-contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .quick-contact-text h2 {
        font-size: 2rem;
    }

    .quick-contact-features {
        justify-content: center;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stat-item {
        padding: 1rem;
    }

    .offer-timer {
        gap: 0.5rem;
    }

    .timer-item {
        min-width: 50px;
    }

    .timer-number {
        font-size: 1.2rem;
    }

    .quick-contact-features {
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .footer-section.main-section {
        padding-left: 0;
    }

    .contact-section {
        padding-right: 0;
    }

    .footer-badges {
        justify-content: center;
    }

    .social-icons {
        justify-content: center;
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .footer-bottom-links {
        justify-content: center;
    }

    .back-to-top {
        bottom: 20px;
        left: 20px;
        width: 45px;
        height: 45px;
        font-size: 1rem;
    }
}

/* التذييل الاحترافي */
.footer {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    padding: 4rem 0 0;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.05)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.05)"/></svg>');
    background-size: 100px 100px;
    animation: float 30s linear infinite;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1.5fr;
    gap: 3rem;
    margin-bottom: 3rem;
    position: relative;
    z-index: 2;
}

.footer-section {
    position: relative;
}

.footer-section.main-section {
    padding-left: 1rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.footer-logo i {
    font-size: 2.5rem;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-logo h3 {
    font-size: 2rem;
    font-weight: 800;
    margin: 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-description {
    font-size: 1rem;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
}

.footer-badges {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.badge-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-full);
    font-size: 0.85rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.badge-item i {
    color: var(--accent-color);
}

.footer-section h4 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: white;
    position: relative;
    padding-bottom: 0.5rem;
}

.footer-section h4::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
}

.footer-links {
    list-style: none;
    margin: 0;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.75rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.95rem;
}

.footer-links a:hover {
    color: white;
    transform: translateX(-5px);
}

.footer-links a i {
    color: var(--secondary-color);
    width: 16px;
    text-align: center;
}

.contact-section {
    padding-right: 1rem;
}

.contact-info {
    margin-bottom: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition-normal);
}

.contact-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.contact-item i {
    font-size: 1.2rem;
    color: var(--accent-color);
    margin-top: 0.2rem;
    width: 20px;
    text-align: center;
}

.contact-item div span {
    display: block;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 0.25rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.contact-item div a {
    color: white;
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition-normal);
}

.contact-item div a:hover {
    color: var(--accent-color);
}

.social-links h5 {
    font-size: 1rem;
    margin-bottom: 1rem;
    color: rgba(255, 255, 255, 0.9);
}

.social-icons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.social-link {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: white;
    transition: var(--transition-bounce);
    border: 2px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.social-link:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: var(--shadow-lg);
}

.social-link.facebook { background: linear-gradient(135deg, #1877f2, #42a5f5); }
.social-link.twitter { background: linear-gradient(135deg, #1da1f2, #42a5f5); }
.social-link.instagram { background: linear-gradient(135deg, #e4405f, #fd1d1d, #fcb045); }
.social-link.linkedin { background: linear-gradient(135deg, #0077b5, #42a5f5); }
.social-link.youtube { background: linear-gradient(135deg, #ff0000, #ff4444); }

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2rem 0;
    position: relative;
    z-index: 2;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.copyright p {
    margin: 0;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.company-info {
    font-size: 0.85rem !important;
    margin-top: 0.5rem !important;
}

.company-info a {
    color: var(--accent-color);
    text-decoration: none;
    font-weight: 600;
}

.company-info a:hover {
    text-decoration: underline;
}

.footer-bottom-links {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.footer-bottom-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition-normal);
}

.footer-bottom-links a:hover {
    color: white;
}

/* زر العودة للأعلى */
.back-to-top {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-bounce);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(100px);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    transform: translateY(-5px) scale(1.1);
    box-shadow: var(--shadow-xl);
}

/* أدوات مساعدة */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }
.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.justify-center { justify-content: center; }
.align-center { align-items: center; }
