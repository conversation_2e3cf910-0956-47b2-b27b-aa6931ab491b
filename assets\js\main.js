/**
 * نقره هوست - JavaScript الأساسي
 * Nakra Host - Main JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // تفعيل القائمة المحمولة
    initMobileMenu();

    // تفعيل النماذج
    initForms();

    // تفعيل الرسائل التلقائية
    initAlerts();

    // تفعيل التمرير السلس
    initSmoothScroll();

    // تفعيل الرسوم المتحركة المتقدمة
    initAdvancedAnimations();

    // تفعيل تأثيرات الرأس
    initHeaderEffects();

    // تفعيل عداد الإحصائيات
    initCounters();

    // تفعيل تأثيرات التمرير
    initScrollEffects();

    // تفعيل تأثيرات الماوس
    initMouseEffects();

    // تفعيل تحميل البيانات التفاعلي
    initLazyLoading();

    // تفعيل العداد التنازلي للعروض
    initOfferTimers();

    // تفعيل الأسئلة الشائعة
    initFAQ();

    // تفعيل زر العودة للأعلى
    initBackToTop();
});

/**
 * تفعيل القائمة المحمولة
 */
function initMobileMenu() {
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const navMenu = document.getElementById('navMenu');
    
    if (mobileMenuToggle && navMenu) {
        mobileMenuToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            
            // تغيير أيقونة القائمة
            const icon = this.querySelector('i');
            if (navMenu.classList.contains('active')) {
                icon.classList.remove('fa-bars');
                icon.classList.add('fa-times');
            } else {
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            }
        });
        
        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!mobileMenuToggle.contains(e.target) && !navMenu.contains(e.target)) {
                navMenu.classList.remove('active');
                const icon = mobileMenuToggle.querySelector('i');
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            }
        });
    }
}

/**
 * تفعيل النماذج
 */
function initForms() {
    // التحقق من صحة النماذج
    const forms = document.querySelectorAll('form[data-validate]');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
            }
        });
    });
    
    // تحسين تجربة المستخدم للحقول
    const inputs = document.querySelectorAll('.form-control');
    inputs.forEach(input => {
        // إضافة تأثيرات التركيز
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
            if (this.value.trim() !== '') {
                this.parentElement.classList.add('filled');
            } else {
                this.parentElement.classList.remove('filled');
            }
        });
        
        // التحقق الفوري
        input.addEventListener('input', function() {
            validateField(this);
        });
    });
}

/**
 * التحقق من صحة النموذج
 */
function validateForm(form) {
    let isValid = true;
    const fields = form.querySelectorAll('.form-control[required]');
    
    fields.forEach(field => {
        if (!validateField(field)) {
            isValid = false;
        }
    });
    
    return isValid;
}

/**
 * التحقق من صحة الحقل
 */
function validateField(field) {
    const value = field.value.trim();
    const type = field.type;
    let isValid = true;
    let message = '';
    
    // إزالة الرسائل السابقة
    removeFieldError(field);
    
    // التحقق من الحقول المطلوبة
    if (field.hasAttribute('required') && value === '') {
        isValid = false;
        message = 'هذا الحقل مطلوب';
    }
    
    // التحقق من البريد الإلكتروني
    else if (type === 'email' && value !== '') {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            isValid = false;
            message = 'يرجى إدخال بريد إلكتروني صحيح';
        }
    }
    
    // التحقق من رقم الهاتف
    else if (field.name === 'phone' && value !== '') {
        const phoneRegex = /^[+]?[0-9\s\-\(\)]{10,}$/;
        if (!phoneRegex.test(value)) {
            isValid = false;
            message = 'يرجى إدخال رقم هاتف صحيح';
        }
    }
    
    // التحقق من كلمة المرور
    else if (type === 'password' && value !== '') {
        if (value.length < 6) {
            isValid = false;
            message = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        }
    }
    
    // التحقق من تأكيد كلمة المرور
    else if (field.name === 'confirm_password' && value !== '') {
        const passwordField = field.form.querySelector('input[name="password"]');
        if (passwordField && value !== passwordField.value) {
            isValid = false;
            message = 'كلمة المرور غير متطابقة';
        }
    }
    
    // عرض رسالة الخطأ
    if (!isValid) {
        showFieldError(field, message);
    }
    
    return isValid;
}

/**
 * عرض رسالة خطأ للحقل
 */
function showFieldError(field, message) {
    field.classList.add('error');
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.textContent = message;
    errorDiv.style.color = '#e74c3c';
    errorDiv.style.fontSize = '0.875rem';
    errorDiv.style.marginTop = '0.25rem';
    
    field.parentElement.appendChild(errorDiv);
}

/**
 * إزالة رسالة خطأ الحقل
 */
function removeFieldError(field) {
    field.classList.remove('error');
    const errorDiv = field.parentElement.querySelector('.field-error');
    if (errorDiv) {
        errorDiv.remove();
    }
}

/**
 * تفعيل الرسائل التلقائية
 */
function initAlerts() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        // إضافة زر الإغلاق
        const closeBtn = document.createElement('button');
        closeBtn.innerHTML = '&times;';
        closeBtn.className = 'alert-close';
        closeBtn.style.cssText = `
            position: absolute;
            top: 10px;
            left: 15px;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            opacity: 0.7;
        `;
        
        alert.style.position = 'relative';
        alert.appendChild(closeBtn);
        
        closeBtn.addEventListener('click', function() {
            alert.style.opacity = '0';
            setTimeout(() => alert.remove(), 300);
        });
        
        // إخفاء تلقائي بعد 5 ثوان
        setTimeout(() => {
            if (alert.parentElement) {
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 300);
            }
        }, 5000);
    });
}

/**
 * تفعيل التمرير السلس
 */
function initSmoothScroll() {
    const links = document.querySelectorAll('a[href^="#"]');
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href === '#') return;
            
            const target = document.querySelector(href);
            if (target) {
                e.preventDefault();
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

/**
 * تفعيل الرسوم المتحركة المتقدمة
 */
function initAdvancedAnimations() {
    // إعدادات مراقب التقاطع المتقدم
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -100px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                const animationType = element.dataset.animation || 'fadeInUp';
                const delay = element.dataset.delay || '0s';

                setTimeout(() => {
                    element.classList.add('animate-in', `animate-${animationType}`);
                }, parseFloat(delay) * 1000);

                observer.unobserve(element);
            }
        });
    }, observerOptions);

    // مراقبة العناصر المتحركة
    const animatedElements = document.querySelectorAll(`
        .animate-fade-in-up, .animate-fade-in-down, .animate-fade-in-left,
        .animate-fade-in-right, .animate-scale-in, .pricing-card,
        .service-card, .feature-item, .section-title, .section-subtitle
    `);

    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = getInitialTransform(el);
        observer.observe(el);
    });

    // إضافة CSS للرسوم المتحركة
    if (!document.getElementById('advanced-animations')) {
        const style = document.createElement('style');
        style.id = 'advanced-animations';
        style.textContent = `
            .animate-in {
                opacity: 1 !important;
                transform: translateY(0) translateX(0) scale(1) !important;
                transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
            }

            .animate-in.animate-fadeInUp {
                animation: fadeInUp 0.8s ease-out forwards;
            }

            .animate-in.animate-fadeInDown {
                animation: fadeInDown 0.8s ease-out forwards;
            }

            .animate-in.animate-fadeInLeft {
                animation: fadeInLeft 0.8s ease-out forwards;
            }

            .animate-in.animate-fadeInRight {
                animation: fadeInRight 0.8s ease-out forwards;
            }

            .animate-in.animate-scaleIn {
                animation: scaleIn 0.6s ease-out forwards;
            }
        `;
        document.head.appendChild(style);
    }
}

function getInitialTransform(element) {
    if (element.classList.contains('animate-fade-in-up') || element.classList.contains('pricing-card')) {
        return 'translateY(50px)';
    } else if (element.classList.contains('animate-fade-in-down')) {
        return 'translateY(-50px)';
    } else if (element.classList.contains('animate-fade-in-left')) {
        return 'translateX(-50px)';
    } else if (element.classList.contains('animate-fade-in-right')) {
        return 'translateX(50px)';
    } else if (element.classList.contains('animate-scale-in')) {
        return 'scale(0.8)';
    }
    return 'translateY(30px)';
}

/**
 * تفعيل تأثيرات الرأس
 */
function initHeaderEffects() {
    const header = document.querySelector('.header');
    let lastScrollY = window.scrollY;

    window.addEventListener('scroll', () => {
        const currentScrollY = window.scrollY;

        if (currentScrollY > 100) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }

        // إخفاء/إظهار الرأس عند التمرير
        if (currentScrollY > lastScrollY && currentScrollY > 200) {
            header.style.transform = 'translateY(-100%)';
        } else {
            header.style.transform = 'translateY(0)';
        }

        lastScrollY = currentScrollY;
    });
}

/**
 * تفعيل عداد الإحصائيات المتقدم
 */
function initCounters() {
    // عداد إحصائيات البطل
    const heroCounters = document.querySelectorAll('.hero-stat-number');

    // عداد الإحصائيات المتقدمة
    const statCounters = document.querySelectorAll('.stat-number');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target;
                const target = parseFloat(counter.dataset.target || counter.textContent.replace(/[^\d.]/g, ''));
                const duration = 2500;
                const isDecimal = target % 1 !== 0;
                const step = target / (duration / 16);
                let current = 0;

                const timer = setInterval(() => {
                    current += step;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }

                    // تحديد التنسيق حسب النوع
                    let displayValue;
                    if (isDecimal) {
                        displayValue = current.toFixed(1);
                    } else {
                        displayValue = Math.floor(current);
                    }

                    // تحديث النص مع الحفاظ على اللاحقات
                    const suffix = counter.parentElement.querySelector('.stat-suffix');
                    if (suffix) {
                        counter.textContent = displayValue;
                    } else if (counter.textContent.includes('%')) {
                        counter.textContent = displayValue + '%';
                    } else if (counter.textContent.includes('+')) {
                        counter.textContent = displayValue + '+';
                    } else if (counter.textContent.includes('/')) {
                        counter.textContent = displayValue + '/7';
                    } else {
                        counter.textContent = displayValue;
                    }

                    // تأثير النبض أثناء العد
                    counter.style.transform = `scale(${1 + Math.sin(current / target * Math.PI) * 0.1})`;
                }, 16);

                // إزالة تأثير النبض عند الانتهاء
                setTimeout(() => {
                    counter.style.transform = 'scale(1)';
                }, duration + 100);

                observer.unobserve(counter);
            }
        });
    }, {
        threshold: 0.5,
        rootMargin: '0px 0px -100px 0px'
    });

    // مراقبة جميع العدادات
    [...heroCounters, ...statCounters].forEach(counter => {
        observer.observe(counter);
    });

    // تأثيرات إضافية للإحصائيات
    statCounters.forEach((counter, index) => {
        const statItem = counter.closest('.stat-item');
        if (statItem) {
            // تأثير التأخير المتدرج
            statItem.style.animationDelay = `${index * 0.2}s`;

            // تأثير الهوفر المتقدم
            statItem.addEventListener('mouseenter', () => {
                const icon = statItem.querySelector('.stat-icon');
                if (icon) {
                    icon.style.transform = 'scale(1.1) rotate(5deg)';
                    icon.style.boxShadow = '0 10px 30px rgba(102, 126, 234, 0.4)';
                }
            });

            statItem.addEventListener('mouseleave', () => {
                const icon = statItem.querySelector('.stat-icon');
                if (icon) {
                    icon.style.transform = 'scale(1) rotate(0deg)';
                    icon.style.boxShadow = 'var(--shadow-lg)';
                }
            });
        }
    });
}

/**
 * تفعيل تأثيرات التمرير
 */
function initScrollEffects() {
    // تأثير المنظر المتوازي للخلفية
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('.hero');

        parallaxElements.forEach(element => {
            const speed = 0.5;
            element.style.transform = `translateY(${scrolled * speed}px)`;
        });
    });

    // تأثير الكشف التدريجي للعناصر
    const revealElements = document.querySelectorAll('.pricing-card, .service-card, .feature-item');

    const revealObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }, index * 100);
            }
        });
    });

    revealElements.forEach(element => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(50px)';
        element.style.transition = 'all 0.6s ease';
        revealObserver.observe(element);
    });
}

/**
 * تفعيل تأثيرات الماوس
 */
function initMouseEffects() {
    // تأثير تتبع الماوس للبطاقات
    const cards = document.querySelectorAll('.pricing-card, .service-card, .feature-item');

    cards.forEach(card => {
        card.addEventListener('mousemove', (e) => {
            const rect = card.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const centerX = rect.width / 2;
            const centerY = rect.height / 2;

            const rotateX = (y - centerY) / 10;
            const rotateY = (centerX - x) / 10;

            card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
        });

        card.addEventListener('mouseleave', () => {
            card.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateZ(0)';
        });
    });

    // تأثير الجسيمات المتحركة عند حركة الماوس
    createMouseParticles();
}

function createMouseParticles() {
    const canvas = document.createElement('canvas');
    canvas.style.position = 'fixed';
    canvas.style.top = '0';
    canvas.style.left = '0';
    canvas.style.width = '100%';
    canvas.style.height = '100%';
    canvas.style.pointerEvents = 'none';
    canvas.style.zIndex = '9999';
    document.body.appendChild(canvas);

    const ctx = canvas.getContext('2d');
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    const particles = [];

    document.addEventListener('mousemove', (e) => {
        particles.push({
            x: e.clientX,
            y: e.clientY,
            size: Math.random() * 3 + 1,
            speedX: (Math.random() - 0.5) * 2,
            speedY: (Math.random() - 0.5) * 2,
            life: 1
        });
    });

    function animateParticles() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        for (let i = particles.length - 1; i >= 0; i--) {
            const particle = particles[i];

            particle.x += particle.speedX;
            particle.y += particle.speedY;
            particle.life -= 0.02;

            if (particle.life <= 0) {
                particles.splice(i, 1);
                continue;
            }

            ctx.save();
            ctx.globalAlpha = particle.life;
            ctx.fillStyle = '#667eea';
            ctx.beginPath();
            ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            ctx.fill();
            ctx.restore();
        }

        requestAnimationFrame(animateParticles);
    }

    animateParticles();

    window.addEventListener('resize', () => {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
    });
}

/**
 * تفعيل التحميل التدريجي
 */
function initLazyLoading() {
    // تحميل الصور بشكل تدريجي
    const images = document.querySelectorAll('img[data-src]');

    const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.add('loaded');
                imageObserver.unobserve(img);
            }
        });
    });

    images.forEach(img => imageObserver.observe(img));

    // تحميل المحتوى التفاعلي
    const lazyElements = document.querySelectorAll('[data-lazy]');

    const contentObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                const content = element.dataset.lazy;

                // محاكاة تحميل المحتوى
                setTimeout(() => {
                    element.innerHTML = content;
                    element.classList.add('loaded');
                }, 500);

                contentObserver.unobserve(element);
            }
        });
    });

    lazyElements.forEach(element => contentObserver.observe(element));
}

/**
 * دوال مساعدة متقدمة
 */

// تحميل المحتوى بـ AJAX مع تأثيرات
function loadContent(url, container, showLoader = true) {
    const containerEl = document.querySelector(container);

    if (showLoader) {
        containerEl.innerHTML = '<div class="loading-spinner">جاري التحميل...</div>';
    }

    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.text();
        })
        .then(html => {
            containerEl.innerHTML = html;
            // إعادة تفعيل الرسوم المتحركة للمحتوى الجديد
            initAdvancedAnimations();
        })
        .catch(error => {
            console.error('خطأ في تحميل المحتوى:', error);
            containerEl.innerHTML = '<div class="error-message">حدث خطأ في تحميل المحتوى</div>';
        });
}

// عرض رسالة تأكيد
function showConfirm(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

// عرض رسالة نجاح
function showSuccess(message) {
    showNotification(message, 'success');
}

// عرض رسالة خطأ
function showError(message) {
    showNotification(message, 'error');
}

// عرض إشعار
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 5px;
        color: white;
        z-index: 9999;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
    `;

    // ألوان حسب النوع
    const colors = {
        success: '#27ae60',
        error: '#e74c3c',
        warning: '#f39c12',
        info: '#3498db'
    };

    notification.style.backgroundColor = colors[type] || colors.info;

    document.body.appendChild(notification);

    // إظهار الإشعار
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 100);

    // إخفاء الإشعار
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

/**
 * تفعيل العداد التنازلي للعروض
 */
function initOfferTimers() {
    const timers = document.querySelectorAll('.offer-timer');

    timers.forEach(timer => {
        const endDate = timer.dataset.end;
        if (!endDate) return;

        const targetDate = new Date(endDate).getTime();

        const updateTimer = () => {
            const now = new Date().getTime();
            const distance = targetDate - now;

            if (distance < 0) {
                timer.innerHTML = '<div class="timer-expired">انتهى العرض</div>';
                return;
            }

            const days = Math.floor(distance / (1000 * 60 * 60 * 24));
            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((distance % (1000 * 60)) / 1000);

            const daysEl = timer.querySelector('.days');
            const hoursEl = timer.querySelector('.hours');
            const minutesEl = timer.querySelector('.minutes');
            const secondsEl = timer.querySelector('.seconds');

            if (daysEl) daysEl.textContent = days.toString().padStart(2, '0');
            if (hoursEl) hoursEl.textContent = hours.toString().padStart(2, '0');
            if (minutesEl) minutesEl.textContent = minutes.toString().padStart(2, '0');
            if (secondsEl) secondsEl.textContent = seconds.toString().padStart(2, '0');

            // تأثير الوميض عند اقتراب انتهاء العرض
            if (distance < 24 * 60 * 60 * 1000) { // أقل من 24 ساعة
                timer.style.animation = 'pulse 1s infinite';
            }

            if (distance < 60 * 60 * 1000) { // أقل من ساعة
                timer.style.animation = 'pulse 0.5s infinite';
                timer.style.color = '#e74c3c';
            }
        };

        // تحديث فوري
        updateTimer();

        // تحديث كل ثانية
        setInterval(updateTimer, 1000);
    });

    // إضافة تأثيرات للعروض المحدودة
    const limitedOffers = document.querySelectorAll('.offer-card.limited-time');
    limitedOffers.forEach(offer => {
        // تأثير النبض للعروض المحدودة
        offer.addEventListener('mouseenter', () => {
            offer.style.animation = 'pulse 0.5s infinite';
        });

        offer.addEventListener('mouseleave', () => {
            offer.style.animation = 'glow 2s ease-in-out infinite';
        });
    });
}

/**
 * تفعيل الأسئلة الشائعة
 */
function initFAQ() {
    const faqItems = document.querySelectorAll('.faq-item');

    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        const answer = item.querySelector('.faq-answer');

        question.addEventListener('click', () => {
            const isActive = item.classList.contains('active');

            // إغلاق جميع الأسئلة الأخرى
            faqItems.forEach(otherItem => {
                if (otherItem !== item) {
                    otherItem.classList.remove('active');
                }
            });

            // تبديل حالة السؤال الحالي
            if (isActive) {
                item.classList.remove('active');
            } else {
                item.classList.add('active');

                // تمرير سلس إلى السؤال المفتوح
                setTimeout(() => {
                    item.scrollIntoView({
                        behavior: 'smooth',
                        block: 'nearest'
                    });
                }, 300);
            }
        });

        // تأثير الهوفر
        question.addEventListener('mouseenter', () => {
            if (!item.classList.contains('active')) {
                question.style.background = 'linear-gradient(135deg, #f8fafc, #e2e8f0)';
            }
        });

        question.addEventListener('mouseleave', () => {
            if (!item.classList.contains('active')) {
                question.style.background = 'linear-gradient(135deg, var(--bg-secondary), white)';
            }
        });
    });

    // فتح أول سؤال افتراضياً
    if (faqItems.length > 0) {
        faqItems[0].classList.add('active');
    }
}

/**
 * تفعيل زر العودة للأعلى
 */
function initBackToTop() {
    const backToTopBtn = document.getElementById('backToTop');

    if (!backToTopBtn) return;

    // إظهار/إخفاء الزر حسب موضع التمرير
    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
            backToTopBtn.classList.add('visible');
        } else {
            backToTopBtn.classList.remove('visible');
        }
    });

    // العودة للأعلى عند النقر
    backToTopBtn.addEventListener('click', () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });

    // تأثير النقر
    backToTopBtn.addEventListener('mousedown', () => {
        backToTopBtn.style.transform = 'translateY(-5px) scale(0.95)';
    });

    backToTopBtn.addEventListener('mouseup', () => {
        backToTopBtn.style.transform = 'translateY(-5px) scale(1.1)';
    });
}
