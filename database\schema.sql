-- قاعدة بيانات نقره هوست
-- Nakra Host Database Schema

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS nakra_host_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE nakra_host_db;

-- جدول المستخدمين
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VA<PERSON>HA<PERSON>(50) NOT NULL,
    phone VARCHAR(20),
    role ENUM('admin', 'customer') DEFAULT 'customer',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    email_verified BOOLEAN DEFAULT FALSE,
    verification_token VARCHAR(100),
    reset_token VARCHAR(100),
    reset_token_expires DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول فئات الخدمات
CREATE TABLE service_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    sort_order INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الخدمات
CREATE TABLE services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_id INT,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    features TEXT,
    price DECIMAL(10,2) NOT NULL,
    setup_fee DECIMAL(10,2) DEFAULT 0,
    billing_cycle ENUM('monthly', 'quarterly', 'semi_annual', 'annual') DEFAULT 'monthly',
    status ENUM('active', 'inactive') DEFAULT 'active',
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES service_categories(id) ON DELETE SET NULL
);

-- جدول الطلبات
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    service_id INT NOT NULL,
    order_number VARCHAR(20) UNIQUE NOT NULL,
    status ENUM('pending', 'processing', 'active', 'suspended', 'cancelled', 'completed') DEFAULT 'pending',
    billing_cycle ENUM('monthly', 'quarterly', 'semi_annual', 'annual') DEFAULT 'monthly',
    amount DECIMAL(10,2) NOT NULL,
    setup_fee DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    domain VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE RESTRICT
);

-- جدول الفواتير
CREATE TABLE invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    order_id INT,
    invoice_number VARCHAR(20) UNIQUE NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    status ENUM('unpaid', 'paid', 'overdue', 'cancelled') DEFAULT 'unpaid',
    due_date DATE NOT NULL,
    paid_date DATETIME,
    payment_method VARCHAR(50),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE SET NULL
);

-- جدول المدفوعات
CREATE TABLE payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT NOT NULL,
    user_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method VARCHAR(50) NOT NULL,
    transaction_id VARCHAR(100),
    status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    gateway_response TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول تذاكر الدعم
CREATE TABLE support_tickets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    ticket_number VARCHAR(20) UNIQUE NOT NULL,
    subject VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    status ENUM('open', 'in_progress', 'waiting_customer', 'closed') DEFAULT 'open',
    assigned_to INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL
);

-- جدول ردود تذاكر الدعم
CREATE TABLE ticket_replies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ticket_id INT NOT NULL,
    user_id INT NOT NULL,
    message TEXT NOT NULL,
    is_staff BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ticket_id) REFERENCES support_tickets(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول الإعدادات
CREATE TABLE settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- إدراج البيانات الأولية
INSERT INTO service_categories (name, description, icon, sort_order) VALUES
('استضافة المواقع', 'خدمات استضافة المواقع الإلكترونية', 'fas fa-server', 1),
('برمجة المواقع', 'خدمات تطوير وبرمجة المواقع الإلكترونية', 'fas fa-code', 2),
('التسويق الإلكتروني', 'خدمات التسويق الرقمي والإعلانات', 'fas fa-bullhorn', 3);

-- إدراج خدمات استضافة أساسية
INSERT INTO services (category_id, name, description, features, price, billing_cycle) VALUES
(1, 'استضافة مشتركة أساسية', 'استضافة مثالية للمواقع الصغيرة والمتوسطة', 'مساحة 10 جيجا\nنقل بيانات غير محدود\nقواعد بيانات MySQL\nبريد إلكتروني\nشهادة SSL مجانية', 99.00, 'monthly'),
(1, 'استضافة مشتركة متقدمة', 'استضافة للمواقع الكبيرة والمتاجر الإلكترونية', 'مساحة 50 جيجا\nنقل بيانات غير محدود\nقواعد بيانات غير محدودة\nبريد إلكتروني غير محدود\nشهادة SSL مجانية\nنسخ احتياطي يومي', 199.00, 'monthly'),
(2, 'تصميم موقع أساسي', 'تصميم موقع إلكتروني احترافي', 'تصميم متجاوب\nلوحة تحكم\nتحسين محركات البحث\nربط وسائل التواصل', 1500.00, 'monthly'),
(3, 'إعلانات جوجل', 'إدارة حملات إعلانات جوجل الإعلانية', 'إنشاء الحملات\nاستهداف الجمهور\nتحليل النتائج\nتقارير شهرية', 500.00, 'monthly');

-- إنشاء مستخدم إداري افتراضي
INSERT INTO users (username, email, password, first_name, last_name, role, status, email_verified) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير', 'النظام', 'admin', 'active', TRUE);

-- إدراج إعدادات أساسية
INSERT INTO settings (setting_key, setting_value, description) VALUES
('site_name', 'نقره هوست', 'اسم الموقع'),
('site_email', '<EMAIL>', 'البريد الإلكتروني للموقع'),
('site_phone', '+966123456789', 'رقم هاتف الموقع'),
('company_name', 'شركة نقرة للتسويق الإلكتروني', 'اسم الشركة'),
('tax_rate', '15', 'معدل الضريبة المضافة'),
('currency', 'SAR', 'العملة'),
('currency_symbol', 'ر.س', 'رمز العملة');
