<?php
require_once 'config/config.php';
require_once 'classes/User.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('auth/login.php');
}

// التحقق من معرف الخدمة
if (!isset($_GET['service_id']) || !is_numeric($_GET['service_id'])) {
    showMessage('معرف الخدمة غير صحيح', 'danger');
    redirect('services.php');
}

$serviceId = (int)$_GET['service_id'];

try {
    $db = Database::getInstance();
    
    // استرجاع بيانات الخدمة
    $stmt = $db->query("
        SELECT s.*, c.name as category_name 
        FROM services s 
        JOIN service_categories c ON s.category_id = c.id 
        WHERE s.id = ? AND s.status = 'active'
    ", [$serviceId]);
    
    $service = $stmt->fetch();
    
    if (!$service) {
        showMessage('الخدمة غير موجودة أو غير متاحة', 'danger');
        redirect('services.php');
    }
    
} catch (Exception $e) {
    showMessage('حدث خطأ في استرجاع بيانات الخدمة', 'danger');
    redirect('services.php');
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $domain = sanitize($_POST['domain'] ?? '');
    $billingCycle = sanitize($_POST['billing_cycle'] ?? $service['billing_cycle']);
    $notes = sanitize($_POST['notes'] ?? '');
    
    // حساب المبلغ حسب دورة الفوترة
    $amount = $service['price'];
    $multiplier = 1;
    
    switch ($billingCycle) {
        case 'quarterly':
            $multiplier = 3;
            break;
        case 'semi_annual':
            $multiplier = 6;
            break;
        case 'annual':
            $multiplier = 12;
            break;
    }
    
    $totalAmount = ($amount * $multiplier) + $service['setup_fee'];
    
    try {
        // إنشاء رقم طلب فريد
        $orderNumber = 'ORD-' . date('Ymd') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        // التحقق من عدم تكرار رقم الطلب
        $stmt = $db->query("SELECT id FROM orders WHERE order_number = ?", [$orderNumber]);
        while ($stmt->fetch()) {
            $orderNumber = 'ORD-' . date('Ymd') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
            $stmt = $db->query("SELECT id FROM orders WHERE order_number = ?", [$orderNumber]);
        }
        
        // إدراج الطلب
        $stmt = $db->query("
            INSERT INTO orders (user_id, service_id, order_number, billing_cycle, amount, setup_fee, total_amount, domain, notes) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ", [
            $_SESSION['user_id'],
            $serviceId,
            $orderNumber,
            $billingCycle,
            $amount * $multiplier,
            $service['setup_fee'],
            $totalAmount,
            $domain,
            $notes
        ]);
        
        $orderId = $db->lastInsertId();
        
        // إنشاء فاتورة
        $invoiceNumber = 'INV-' . date('Ymd') . '-' . str_pad($orderId, 6, '0', STR_PAD_LEFT);
        $dueDate = date('Y-m-d', strtotime('+7 days'));
        
        $stmt = $db->query("
            INSERT INTO invoices (user_id, order_id, invoice_number, amount, total_amount, due_date) 
            VALUES (?, ?, ?, ?, ?, ?)
        ", [
            $_SESSION['user_id'],
            $orderId,
            $invoiceNumber,
            $totalAmount,
            $totalAmount,
            $dueDate
        ]);
        
        $success = 'تم إنشاء الطلب بنجاح! رقم الطلب: ' . $orderNumber;
        
        // إعادة توجيه إلى صفحة الطلبات
        showMessage($success, 'success');
        header("refresh:3;url=dashboard/orders.php");
        
    } catch (Exception $e) {
        $error = 'حدث خطأ في إنشاء الطلب. يرجى المحاولة مرة أخرى.';
    }
}

$message = getMessage();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلب خدمة: <?php echo htmlspecialchars($service['name']); ?> - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .order-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .service-summary {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .service-summary h2 {
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
        
        .price-calculator {
            background: var(--light-color);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            margin-bottom: 2rem;
        }
        
        .price-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        
        .price-total {
            border-top: 2px solid var(--primary-color);
            padding-top: 1rem;
            margin-top: 1rem;
            font-weight: 700;
            font-size: 1.2rem;
        }
        
        .order-form {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 2rem;
        }
        
        .billing-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .billing-option {
            position: relative;
        }
        
        .billing-option input[type="radio"] {
            position: absolute;
            opacity: 0;
        }
        
        .billing-option label {
            display: block;
            padding: 1rem;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            text-align: center;
            cursor: pointer;
            transition: var(--transition);
        }
        
        .billing-option input[type="radio"]:checked + label {
            border-color: var(--secondary-color);
            background-color: rgba(52, 152, 219, 0.1);
        }
        
        .billing-price {
            font-weight: 700;
            color: var(--secondary-color);
            font-size: 1.1rem;
        }
        
        .billing-savings {
            color: var(--success-color);
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <!-- الرأس -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="index.php" class="logo">
                    <i class="fas fa-server"></i> نقره هوست
                </a>
                
                <ul class="nav-menu" id="navMenu">
                    <li><a href="index.php">الرئيسية</a></li>
                    <li><a href="services.php">خدماتنا</a></li>
                    <li><a href="dashboard/">لوحة التحكم</a></li>
                    <li><a href="auth/logout.php">تسجيل الخروج</a></li>
                </ul>
                
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </nav>
        </div>
    </header>

    <div class="order-container">
        <!-- عرض الرسائل -->
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $message['type']; ?>">
                <?php echo $message['message']; ?>
            </div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
            </div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                <br><small>جاري إعادة التوجيه إلى صفحة الطلبات...</small>
            </div>
        <?php endif; ?>

        <!-- ملخص الخدمة -->
        <div class="service-summary">
            <h2><i class="fas fa-shopping-cart"></i> طلب خدمة جديدة</h2>
            <h3><?php echo htmlspecialchars($service['name']); ?></h3>
            <p><strong>الفئة:</strong> <?php echo htmlspecialchars($service['category_name']); ?></p>
            <p><?php echo htmlspecialchars($service['description']); ?></p>
            
            <?php if ($service['features']): ?>
                <h4>المميزات المتضمنة:</h4>
                <ul>
                    <?php
                    $features = explode("\n", $service['features']);
                    foreach ($features as $feature):
                        $feature = trim($feature);
                        if (!empty($feature)):
                    ?>
                        <li><?php echo htmlspecialchars($feature); ?></li>
                    <?php 
                        endif;
                    endforeach; 
                    ?>
                </ul>
            <?php endif; ?>
        </div>

        <!-- نموذج الطلب -->
        <div class="order-form">
            <h3><i class="fas fa-edit"></i> تفاصيل الطلب</h3>
            
            <form method="POST" data-validate id="orderForm">
                <!-- خيارات دورة الفوترة -->
                <div class="form-group">
                    <label class="form-label">اختر دورة الفوترة:</label>
                    <div class="billing-options">
                        <div class="billing-option">
                            <input type="radio" id="monthly" name="billing_cycle" value="monthly" 
                                   <?php echo $service['billing_cycle'] === 'monthly' ? 'checked' : ''; ?>>
                            <label for="monthly">
                                <div>شهرياً</div>
                                <div class="billing-price"><?php echo number_format($service['price'], 0); ?> <?php echo CURRENCY_SYMBOL; ?></div>
                            </label>
                        </div>
                        
                        <div class="billing-option">
                            <input type="radio" id="quarterly" name="billing_cycle" value="quarterly">
                            <label for="quarterly">
                                <div>كل 3 أشهر</div>
                                <div class="billing-price"><?php echo number_format($service['price'] * 3, 0); ?> <?php echo CURRENCY_SYMBOL; ?></div>
                                <div class="billing-savings">وفر 5%</div>
                            </label>
                        </div>
                        
                        <div class="billing-option">
                            <input type="radio" id="semi_annual" name="billing_cycle" value="semi_annual">
                            <label for="semi_annual">
                                <div>كل 6 أشهر</div>
                                <div class="billing-price"><?php echo number_format($service['price'] * 6 * 0.9, 0); ?> <?php echo CURRENCY_SYMBOL; ?></div>
                                <div class="billing-savings">وفر 10%</div>
                            </label>
                        </div>
                        
                        <div class="billing-option">
                            <input type="radio" id="annual" name="billing_cycle" value="annual">
                            <label for="annual">
                                <div>سنوياً</div>
                                <div class="billing-price"><?php echo number_format($service['price'] * 12 * 0.8, 0); ?> <?php echo CURRENCY_SYMBOL; ?></div>
                                <div class="billing-savings">وفر 20%</div>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- النطاق -->
                <div class="form-group">
                    <label for="domain" class="form-label">
                        <i class="fas fa-globe"></i> النطاق (اختياري)
                    </label>
                    <input 
                        type="text" 
                        id="domain" 
                        name="domain" 
                        class="form-control"
                        placeholder="مثال: example.com"
                    >
                    <small>إذا كان لديك نطاق موجود أو تريد تسجيل نطاق جديد</small>
                </div>

                <!-- ملاحظات -->
                <div class="form-group">
                    <label for="notes" class="form-label">
                        <i class="fas fa-sticky-note"></i> ملاحظات إضافية (اختياري)
                    </label>
                    <textarea 
                        id="notes" 
                        name="notes" 
                        class="form-control" 
                        rows="4"
                        placeholder="أي متطلبات خاصة أو ملاحظات تريد إضافتها"
                    ></textarea>
                </div>

                <!-- حاسبة السعر -->
                <div class="price-calculator">
                    <h4><i class="fas fa-calculator"></i> ملخص التكلفة</h4>
                    <div class="price-row">
                        <span>سعر الخدمة:</span>
                        <span id="servicePrice"><?php echo number_format($service['price'], 2); ?> <?php echo CURRENCY_SYMBOL; ?></span>
                    </div>
                    <?php if ($service['setup_fee'] > 0): ?>
                    <div class="price-row">
                        <span>رسوم الإعداد:</span>
                        <span><?php echo number_format($service['setup_fee'], 2); ?> <?php echo CURRENCY_SYMBOL; ?></span>
                    </div>
                    <?php endif; ?>
                    <div class="price-row price-total">
                        <span>المجموع:</span>
                        <span id="totalPrice"><?php echo number_format($service['price'] + $service['setup_fee'], 2); ?> <?php echo CURRENCY_SYMBOL; ?></span>
                    </div>
                </div>

                <!-- أزرار الإجراء -->
                <div class="form-group text-center">
                    <button type="submit" class="btn btn-primary" style="margin-left: 1rem;">
                        <i class="fas fa-shopping-cart"></i> تأكيد الطلب
                    </button>
                    <a href="services.php" class="btn btn-outline">
                        <i class="fas fa-arrow-right"></i> العودة للخدمات
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script>
        // حاسبة السعر
        const servicePrice = <?php echo $service['price']; ?>;
        const setupFee = <?php echo $service['setup_fee']; ?>;
        
        function updatePrice() {
            const billingCycle = document.querySelector('input[name="billing_cycle"]:checked').value;
            let multiplier = 1;
            let discount = 1;
            
            switch (billingCycle) {
                case 'quarterly':
                    multiplier = 3;
                    discount = 0.95;
                    break;
                case 'semi_annual':
                    multiplier = 6;
                    discount = 0.9;
                    break;
                case 'annual':
                    multiplier = 12;
                    discount = 0.8;
                    break;
            }
            
            const totalServicePrice = servicePrice * multiplier * discount;
            const totalPrice = totalServicePrice + setupFee;
            
            document.getElementById('servicePrice').textContent = 
                new Intl.NumberFormat('ar-SA').format(totalServicePrice.toFixed(2)) + ' <?php echo CURRENCY_SYMBOL; ?>';
            document.getElementById('totalPrice').textContent = 
                new Intl.NumberFormat('ar-SA').format(totalPrice.toFixed(2)) + ' <?php echo CURRENCY_SYMBOL; ?>';
        }
        
        // تحديث السعر عند تغيير دورة الفوترة
        document.querySelectorAll('input[name="billing_cycle"]').forEach(radio => {
            radio.addEventListener('change', updatePrice);
        });
        
        // تحديث السعر عند تحميل الصفحة
        updatePrice();
    </script>
</body>
</html>
