<?php
require_once 'config/config.php';

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize($_POST['name']);
    $email = sanitize($_POST['email']);
    $phone = sanitize($_POST['phone']);
    $subject = sanitize($_POST['subject']);
    $message = sanitize($_POST['message']);
    
    // التحقق من البيانات
    if (empty($name) || empty($email) || empty($subject) || empty($message)) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'البريد الإلكتروني غير صحيح';
    } else {
        // إرسال البريد الإلكتروني
        $to = SITE_EMAIL;
        $emailSubject = "رسالة جديدة من موقع " . SITE_NAME . ": " . $subject;
        $emailMessage = "
        <html>
        <body style='font-family: Arial, sans-serif; direction: rtl;'>
            <h2>رسالة جديدة من موقع " . SITE_NAME . "</h2>
            <p><strong>الاسم:</strong> {$name}</p>
            <p><strong>البريد الإلكتروني:</strong> {$email}</p>
            <p><strong>رقم الهاتف:</strong> {$phone}</p>
            <p><strong>الموضوع:</strong> {$subject}</p>
            <p><strong>الرسالة:</strong></p>
            <p>{$message}</p>
            <hr>
            <p><small>تم إرسال هذه الرسالة من نموذج الاتصال في موقع " . SITE_NAME . "</small></p>
        </body>
        </html>
        ";
        
        $headers = "MIME-Version: 1.0" . "\r\n";
        $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
        $headers .= "From: " . $name . " <" . $email . ">" . "\r\n";
        $headers .= "Reply-To: " . $email . "\r\n";
        
        if (mail($to, $emailSubject, $emailMessage, $headers)) {
            $success = 'تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.';
            // مسح البيانات بعد الإرسال الناجح
            $name = $email = $phone = $subject = $message = '';
        } else {
            $error = 'حدث خطأ في إرسال الرسالة. يرجى المحاولة مرة أخرى أو التواصل معنا مباشرة.';
        }
    }
}

$pageMessage = getMessage();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اتصل بنا - <?php echo SITE_NAME; ?></title>
    <meta name="description" content="تواصل مع فريق نقره هوست للحصول على استشارة مجانية حول خدمات الاستضافة والبرمجة والتسويق الإلكتروني">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .contact-hero {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 3rem 0;
            text-align: center;
        }
        
        .contact-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 3rem 1rem;
        }
        
        .contact-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            margin-bottom: 3rem;
        }
        
        .contact-info {
            background: white;
            padding: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        
        .contact-form {
            background: white;
            padding: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            margin-bottom: 2rem;
            padding: 1rem;
            background: var(--light-color);
            border-radius: var(--border-radius);
        }
        
        .contact-icon {
            font-size: 2rem;
            color: var(--secondary-color);
            margin-left: 1rem;
            width: 60px;
            text-align: center;
        }
        
        .contact-details h4 {
            margin-bottom: 0.5rem;
            color: var(--primary-color);
        }
        
        .contact-details p {
            margin: 0;
            color: #666;
        }
        
        .map-container {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            overflow: hidden;
            height: 400px;
            margin-top: 2rem;
        }
        
        .map-placeholder {
            width: 100%;
            height: 100%;
            background: var(--light-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 1.2rem;
        }
        
        .social-links {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .social-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            background: var(--secondary-color);
            color: white;
            border-radius: 50%;
            text-decoration: none;
            transition: var(--transition);
        }
        
        .social-link:hover {
            background: var(--primary-color);
            transform: translateY(-3px);
        }
        
        @media (max-width: 768px) {
            .contact-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            
            .contact-item {
                flex-direction: column;
                text-align: center;
            }
            
            .contact-icon {
                margin-left: 0;
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- الرأس -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="index.php" class="logo">
                    <i class="fas fa-server"></i> نقره هوست
                </a>
                
                <ul class="nav-menu" id="navMenu">
                    <li><a href="index.php">الرئيسية</a></li>
                    <li><a href="services.php">خدماتنا</a></li>
                    <li><a href="hosting.php">الاستضافة</a></li>
                    <li><a href="development.php">البرمجة</a></li>
                    <li><a href="marketing.php">التسويق</a></li>
                    <li><a href="support.php">الدعم</a></li>
                    <li><a href="contact.php">اتصل بنا</a></li>
                    <?php if (isLoggedIn()): ?>
                        <li><a href="dashboard/">لوحة التحكم</a></li>
                        <li><a href="auth/logout.php">تسجيل الخروج</a></li>
                    <?php else: ?>
                        <li><a href="auth/login.php">تسجيل الدخول</a></li>
                        <li><a href="auth/register.php">إنشاء حساب</a></li>
                    <?php endif; ?>
                </ul>
                
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </nav>
        </div>
    </header>

    <!-- قسم البطل -->
    <section class="contact-hero">
        <div class="container">
            <h1>تواصل معنا</h1>
            <p>نحن هنا لمساعدتك في تحقيق أهدافك الرقمية</p>
        </div>
    </section>

    <div class="contact-container">
        <!-- عرض الرسائل -->
        <?php if ($pageMessage): ?>
            <div class="alert alert-<?php echo $pageMessage['type']; ?>">
                <?php echo $pageMessage['message']; ?>
            </div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
            </div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo $success; ?>
            </div>
        <?php endif; ?>

        <!-- شبكة الاتصال -->
        <div class="contact-grid">
            <!-- معلومات الاتصال -->
            <div class="contact-info">
                <h2><i class="fas fa-info-circle"></i> معلومات الاتصال</h2>
                <p>تواصل معنا عبر أي من الطرق التالية وسنكون سعداء لخدمتك</p>
                
                <div class="contact-item">
                    <div class="contact-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="contact-details">
                        <h4>البريد الإلكتروني</h4>
                        <p><?php echo SITE_EMAIL; ?></p>
                        <p>نرد على رسائلك خلال 24 ساعة</p>
                    </div>
                </div>
                
                <div class="contact-item">
                    <div class="contact-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <div class="contact-details">
                        <h4>رقم الهاتف</h4>
                        <p><?php echo SITE_PHONE; ?></p>
                        <p>متاح من السبت إلى الخميس 9 ص - 6 م</p>
                    </div>
                </div>
                
                <div class="contact-item">
                    <div class="contact-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="contact-details">
                        <h4>الشركة الأم</h4>
                        <p><?php echo COMPANY_NAME; ?></p>
                        <p><a href="<?php echo COMPANY_WEBSITE; ?>" target="_blank"><?php echo COMPANY_WEBSITE; ?></a></p>
                    </div>
                </div>
                
                <div class="contact-item">
                    <div class="contact-icon">
                        <i class="fas fa-handshake"></i>
                    </div>
                    <div class="contact-details">
                        <h4>شريكنا</h4>
                        <p>هوست ميد</p>
                        <p><a href="<?php echo HOSTMEED_URL; ?>" target="_blank"><?php echo HOSTMEED_URL; ?></a></p>
                    </div>
                </div>
                
                <!-- روابط التواصل الاجتماعي -->
                <h3>تابعنا على</h3>
                <div class="social-links">
                    <a href="#" class="social-link" title="فيسبوك">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="social-link" title="تويتر">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="social-link" title="إنستغرام">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="#" class="social-link" title="لينكد إن">
                        <i class="fab fa-linkedin-in"></i>
                    </a>
                    <a href="#" class="social-link" title="واتساب">
                        <i class="fab fa-whatsapp"></i>
                    </a>
                </div>
            </div>
            
            <!-- نموذج الاتصال -->
            <div class="contact-form">
                <h2><i class="fas fa-paper-plane"></i> أرسل لنا رسالة</h2>
                <p>املأ النموذج أدناه وسنتواصل معك في أقرب وقت ممكن</p>
                
                <form method="POST" data-validate>
                    <div class="form-group">
                        <label for="name" class="form-label">
                            <i class="fas fa-user"></i> الاسم الكامل *
                        </label>
                        <input 
                            type="text" 
                            id="name" 
                            name="name" 
                            class="form-control" 
                            required
                            value="<?php echo isset($name) ? htmlspecialchars($name) : ''; ?>"
                            placeholder="اسمك الكامل"
                        >
                    </div>
                    
                    <div class="form-group">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope"></i> البريد الإلكتروني *
                        </label>
                        <input 
                            type="email" 
                            id="email" 
                            name="email" 
                            class="form-control" 
                            required
                            value="<?php echo isset($email) ? htmlspecialchars($email) : ''; ?>"
                            placeholder="بريدك الإلكتروني"
                        >
                    </div>
                    
                    <div class="form-group">
                        <label for="phone" class="form-label">
                            <i class="fas fa-phone"></i> رقم الهاتف
                        </label>
                        <input 
                            type="tel" 
                            id="phone" 
                            name="phone" 
                            class="form-control"
                            value="<?php echo isset($phone) ? htmlspecialchars($phone) : ''; ?>"
                            placeholder="رقم هاتفك"
                        >
                    </div>
                    
                    <div class="form-group">
                        <label for="subject" class="form-label">
                            <i class="fas fa-tag"></i> الموضوع *
                        </label>
                        <select id="subject" name="subject" class="form-control" required>
                            <option value="">اختر الموضوع</option>
                            <option value="استفسار عام" <?php echo (isset($subject) && $subject === 'استفسار عام') ? 'selected' : ''; ?>>استفسار عام</option>
                            <option value="خدمات الاستضافة" <?php echo (isset($subject) && $subject === 'خدمات الاستضافة') ? 'selected' : ''; ?>>خدمات الاستضافة</option>
                            <option value="برمجة المواقع" <?php echo (isset($subject) && $subject === 'برمجة المواقع') ? 'selected' : ''; ?>>برمجة المواقع</option>
                            <option value="التسويق الإلكتروني" <?php echo (isset($subject) && $subject === 'التسويق الإلكتروني') ? 'selected' : ''; ?>>التسويق الإلكتروني</option>
                            <option value="الدعم الفني" <?php echo (isset($subject) && $subject === 'الدعم الفني') ? 'selected' : ''; ?>>الدعم الفني</option>
                            <option value="الشراكة والتعاون" <?php echo (isset($subject) && $subject === 'الشراكة والتعاون') ? 'selected' : ''; ?>>الشراكة والتعاون</option>
                            <option value="شكوى" <?php echo (isset($subject) && $subject === 'شكوى') ? 'selected' : ''; ?>>شكوى</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="message" class="form-label">
                            <i class="fas fa-comment"></i> الرسالة *
                        </label>
                        <textarea 
                            id="message" 
                            name="message" 
                            class="form-control" 
                            rows="6" 
                            required
                            placeholder="اكتب رسالتك هنا..."
                        ><?php echo isset($message) ? htmlspecialchars($message) : ''; ?></textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-primary" style="width: 100%;">
                        <i class="fas fa-paper-plane"></i> إرسال الرسالة
                    </button>
                </form>
            </div>
        </div>
        
        <!-- خريطة (placeholder) -->
        <div class="map-container">
            <div class="map-placeholder">
                <div class="text-center">
                    <i class="fas fa-map-marker-alt fa-3x" style="color: var(--secondary-color); margin-bottom: 1rem;"></i>
                    <h3>موقعنا</h3>
                    <p>المملكة العربية السعودية</p>
                    <p>يمكنك إضافة خريطة Google Maps هنا</p>
                </div>
            </div>
        </div>
    </div>

    <!-- التذييل -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>نقره هوست</h3>
                    <p>شريكك الموثوق في عالم الاستضافة والبرمجة والتسويق الإلكتروني</p>
                    <p>خدمات مقدمة من <a href="<?php echo COMPANY_WEBSITE; ?>" target="_blank">شركة نقرة للتسويق الإلكتروني</a></p>
                </div>
                
                <div class="footer-section">
                    <h3>خدماتنا</h3>
                    <ul style="list-style: none;">
                        <li><a href="hosting.php">استضافة المواقع</a></li>
                        <li><a href="development.php">برمجة المواقع</a></li>
                        <li><a href="marketing.php">التسويق الإلكتروني</a></li>
                        <li><a href="support.php">الدعم الفني</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3>روابط مهمة</h3>
                    <ul style="list-style: none;">
                        <li><a href="about.php">من نحن</a></li>
                        <li><a href="contact.php">اتصل بنا</a></li>
                        <li><a href="terms.php">الشروط والأحكام</a></li>
                        <li><a href="privacy.php">سياسة الخصوصية</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3>تواصل معنا</h3>
                    <p><i class="fas fa-envelope"></i> <?php echo SITE_EMAIL; ?></p>
                    <p><i class="fas fa-phone"></i> <?php echo SITE_PHONE; ?></p>
                    <div class="mt-2">
                        <a href="#" style="margin-left: 10px;"><i class="fab fa-facebook fa-2x"></i></a>
                        <a href="#" style="margin-left: 10px;"><i class="fab fa-twitter fa-2x"></i></a>
                        <a href="#" style="margin-left: 10px;"><i class="fab fa-instagram fa-2x"></i></a>
                        <a href="#"><i class="fab fa-linkedin fa-2x"></i></a>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
</body>
</html>
