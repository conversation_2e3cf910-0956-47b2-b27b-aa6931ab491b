<?php
/**
 * فئة المستخدم - نقره هوست
 * User Class - Nakra Host
 */

class User {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * تسجيل مستخدم جديد
     */
    public function register($data) {
        try {
            // التحقق من وجود البريد الإلكتروني
            if ($this->emailExists($data['email'])) {
                throw new Exception('البريد الإلكتروني مستخدم بالفعل');
            }
            
            // التحقق من وجود اسم المستخدم
            if ($this->usernameExists($data['username'])) {
                throw new Exception('اسم المستخدم مستخدم بالفعل');
            }
            
            // تشفير كلمة المرور
            $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
            
            // إنشاء رمز التحقق
            $verificationToken = bin2hex(random_bytes(32));
            
            // إدراج المستخدم
            $sql = "INSERT INTO users (username, email, password, first_name, last_name, phone, verification_token) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $this->db->query($sql, [
                $data['username'],
                $data['email'],
                $hashedPassword,
                $data['first_name'],
                $data['last_name'],
                $data['phone'] ?? null,
                $verificationToken
            ]);
            
            $userId = $this->db->lastInsertId();
            
            // إرسال بريد التحقق
            $this->sendVerificationEmail($data['email'], $verificationToken);
            
            return [
                'success' => true,
                'user_id' => $userId,
                'message' => 'تم إنشاء الحساب بنجاح. يرجى التحقق من بريدك الإلكتروني'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * تسجيل الدخول
     */
    public function login($email, $password) {
        try {
            $sql = "SELECT * FROM users WHERE email = ? AND status = 'active'";
            $stmt = $this->db->query($sql, [$email]);
            $user = $stmt->fetch();
            
            if (!$user) {
                throw new Exception('البريد الإلكتروني أو كلمة المرور غير صحيحة');
            }
            
            if (!password_verify($password, $user['password'])) {
                throw new Exception('البريد الإلكتروني أو كلمة المرور غير صحيحة');
            }
            
            // بدء الجلسة
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['full_name'] = $user['first_name'] . ' ' . $user['last_name'];
            
            // تحديث آخر تسجيل دخول
            $this->updateLastLogin($user['id']);
            
            return [
                'success' => true,
                'user' => $user,
                'message' => 'تم تسجيل الدخول بنجاح'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * تسجيل الخروج
     */
    public function logout() {
        session_destroy();
        return [
            'success' => true,
            'message' => 'تم تسجيل الخروج بنجاح'
        ];
    }
    
    /**
     * التحقق من البريد الإلكتروني
     */
    public function verifyEmail($token) {
        try {
            $sql = "SELECT id FROM users WHERE verification_token = ?";
            $stmt = $this->db->query($sql, [$token]);
            $user = $stmt->fetch();
            
            if (!$user) {
                throw new Exception('رمز التحقق غير صحيح');
            }
            
            // تحديث حالة التحقق
            $sql = "UPDATE users SET email_verified = 1, verification_token = NULL WHERE id = ?";
            $this->db->query($sql, [$user['id']]);
            
            return [
                'success' => true,
                'message' => 'تم التحقق من البريد الإلكتروني بنجاح'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * إعادة تعيين كلمة المرور
     */
    public function resetPassword($email) {
        try {
            $sql = "SELECT id FROM users WHERE email = ?";
            $stmt = $this->db->query($sql, [$email]);
            $user = $stmt->fetch();
            
            if (!$user) {
                throw new Exception('البريد الإلكتروني غير موجود');
            }
            
            // إنشاء رمز إعادة التعيين
            $resetToken = bin2hex(random_bytes(32));
            $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));
            
            // تحديث رمز إعادة التعيين
            $sql = "UPDATE users SET reset_token = ?, reset_token_expires = ? WHERE id = ?";
            $this->db->query($sql, [$resetToken, $expires, $user['id']]);
            
            // إرسال بريد إعادة التعيين
            $this->sendResetEmail($email, $resetToken);
            
            return [
                'success' => true,
                'message' => 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * تحديث كلمة المرور
     */
    public function updatePassword($token, $newPassword) {
        try {
            $sql = "SELECT id FROM users WHERE reset_token = ? AND reset_token_expires > NOW()";
            $stmt = $this->db->query($sql, [$token]);
            $user = $stmt->fetch();
            
            if (!$user) {
                throw new Exception('رمز إعادة التعيين غير صحيح أو منتهي الصلاحية');
            }
            
            // تشفير كلمة المرور الجديدة
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            
            // تحديث كلمة المرور
            $sql = "UPDATE users SET password = ?, reset_token = NULL, reset_token_expires = NULL WHERE id = ?";
            $this->db->query($sql, [$hashedPassword, $user['id']]);
            
            return [
                'success' => true,
                'message' => 'تم تحديث كلمة المرور بنجاح'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * الحصول على بيانات المستخدم
     */
    public function getUserById($id) {
        $sql = "SELECT * FROM users WHERE id = ?";
        $stmt = $this->db->query($sql, [$id]);
        return $stmt->fetch();
    }
    
    /**
     * تحديث بيانات المستخدم
     */
    public function updateProfile($userId, $data) {
        try {
            $sql = "UPDATE users SET first_name = ?, last_name = ?, phone = ? WHERE id = ?";
            $this->db->query($sql, [
                $data['first_name'],
                $data['last_name'],
                $data['phone'],
                $userId
            ]);
            
            return [
                'success' => true,
                'message' => 'تم تحديث البيانات بنجاح'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * التحقق من وجود البريد الإلكتروني
     */
    private function emailExists($email) {
        $sql = "SELECT id FROM users WHERE email = ?";
        $stmt = $this->db->query($sql, [$email]);
        return $stmt->fetch() !== false;
    }
    
    /**
     * التحقق من وجود اسم المستخدم
     */
    private function usernameExists($username) {
        $sql = "SELECT id FROM users WHERE username = ?";
        $stmt = $this->db->query($sql, [$username]);
        return $stmt->fetch() !== false;
    }
    
    /**
     * تحديث آخر تسجيل دخول
     */
    private function updateLastLogin($userId) {
        $sql = "UPDATE users SET updated_at = NOW() WHERE id = ?";
        $this->db->query($sql, [$userId]);
    }
    
    /**
     * إرسال بريد التحقق
     */
    private function sendVerificationEmail($email, $token) {
        $verificationLink = SITE_URL . "/auth/verify.php?token=" . $token;
        
        $subject = "تحقق من بريدك الإلكتروني - " . SITE_NAME;
        $message = "
        <html>
        <body style='font-family: Arial, sans-serif; direction: rtl;'>
            <h2>مرحباً بك في " . SITE_NAME . "</h2>
            <p>شكراً لك على التسجيل في موقعنا. يرجى النقر على الرابط أدناه للتحقق من بريدك الإلكتروني:</p>
            <p><a href='{$verificationLink}' style='background-color: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تحقق من البريد الإلكتروني</a></p>
            <p>إذا لم تقم بإنشاء هذا الحساب، يرجى تجاهل هذا البريد.</p>
            <p>مع تحيات فريق " . SITE_NAME . "</p>
        </body>
        </html>
        ";
        
        $this->sendEmail($email, $subject, $message);
    }
    
    /**
     * إرسال بريد إعادة تعيين كلمة المرور
     */
    private function sendResetEmail($email, $token) {
        $resetLink = SITE_URL . "/auth/reset-password.php?token=" . $token;
        
        $subject = "إعادة تعيين كلمة المرور - " . SITE_NAME;
        $message = "
        <html>
        <body style='font-family: Arial, sans-serif; direction: rtl;'>
            <h2>إعادة تعيين كلمة المرور</h2>
            <p>تم طلب إعادة تعيين كلمة المرور لحسابك. يرجى النقر على الرابط أدناه لإعادة تعيين كلمة المرور:</p>
            <p><a href='{$resetLink}' style='background-color: #e74c3c; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إعادة تعيين كلمة المرور</a></p>
            <p>هذا الرابط صالح لمدة ساعة واحدة فقط.</p>
            <p>إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذا البريد.</p>
            <p>مع تحيات فريق " . SITE_NAME . "</p>
        </body>
        </html>
        ";
        
        $this->sendEmail($email, $subject, $message);
    }
    
    /**
     * إرسال بريد إلكتروني
     */
    private function sendEmail($to, $subject, $message) {
        $headers = "MIME-Version: 1.0" . "\r\n";
        $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
        $headers .= "From: " . SITE_NAME . " <" . SITE_EMAIL . ">" . "\r\n";
        
        return mail($to, $subject, $message, $headers);
    }
}
