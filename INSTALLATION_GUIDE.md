# دليل تثبيت وتشغيل موقع نقره هوست

## نظرة عامة
هذا الدليل يوضح كيفية تثبيت وتشغيل موقع نقره هوست على الاستضافة المشتركة أو الخادم المحلي.

## المتطلبات الأساسية

### متطلبات الخادم
- **PHP**: الإصدار 7.4 أو أحدث
- **MySQL**: الإصدار 5.7 أو أحدث
- **Apache/Nginx**: مع دعم mod_rewrite
- **مساحة القرص**: 100 ميجابايت على الأقل
- **الذاكرة**: 128 ميجابايت على الأقل

### امتدادات PHP المطلوبة
- PDO
- PDO_MySQL
- mbstring
- openssl
- curl
- gd (اختياري للصور)

## خطوات التثبيت

### 1. تحميل الملفات
```bash
# رفع جميع ملفات المشروع إلى مجلد الاستضافة
# عادة public_html أو www أو htdocs
```

### 2. إعداد صلاحيات المجلدات
```bash
chmod 755 config/
chmod 755 uploads/
chmod 644 .htaccess
```

### 3. تشغيل معالج التثبيت
1. افتح المتصفح واذهب إلى: `http://yourdomain.com/install.php`
2. اتبع الخطوات في معالج التثبيت:
   - **الخطوة 1**: فحص المتطلبات
   - **الخطوة 2**: إعداد قاعدة البيانات
   - **الخطوة 3**: إنشاء حساب المدير
   - **الخطوة 4**: إنهاء التثبيت

### 4. إعداد قاعدة البيانات يدوياً (اختياري)
إذا كنت تفضل الإعداد اليدوي:

```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE nakra_host_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استيراد الهيكل
mysql -u username -p nakra_host_db < database/schema.sql
```

### 5. تحديث إعدادات قاعدة البيانات
عدّل ملف `config/database.php`:

```php
private $host = 'localhost';           // خادم قاعدة البيانات
private $dbname = 'your_database';     // اسم قاعدة البيانات
private $username = 'your_username';   // اسم المستخدم
private $password = 'your_password';   // كلمة المرور
```

### 6. تحديث الإعدادات العامة
عدّل ملف `config/config.php`:

```php
define('SITE_URL', 'https://nakra.host');
define('SITE_EMAIL', '<EMAIL>');
define('SITE_PHONE', '+966123456789');
// ... باقي الإعدادات
```

### 7. حذف ملف التثبيت
```bash
rm install.php
```

## إعداد البريد الإلكتروني

### SMTP (موصى به)
عدّل إعدادات SMTP في `config/config.php`:

```php
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('SMTP_ENCRYPTION', 'tls');
```

## الأمان والحماية

### 1. تحديث كلمات المرور
- غيّر كلمة مرور قاعدة البيانات
- استخدم كلمة مرور قوية لحساب المدير
- حدّث مفتاح التشفير في `config/config.php`

### 2. إعدادات SSL
```apache
# في ملف .htaccess لإجبار HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

### 3. النسخ الاحتياطية
```bash
# نسخة احتياطية لقاعدة البيانات
mysqldump -u username -p database_name > backup.sql

# نسخة احتياطية للملفات
tar -czf website_backup.tar.gz /path/to/website/
```

## اختبار التثبيت

### 1. اختبار الصفحة الرئيسية
- افتح `http://yourdomain.com`
- تأكد من ظهور الصفحة بشكل صحيح

### 2. اختبار تسجيل الدخول
- اذهب إلى `/auth/login.php`
- سجل دخول بحساب المدير

### 3. اختبار لوحة التحكم
- اذهب إلى `/dashboard/`
- تأكد من عمل جميع الوظائف

### 4. اختبار إنشاء حساب
- اذهب إلى `/auth/register.php`
- أنشئ حساب تجريبي

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### خطأ في الاتصال بقاعدة البيانات
```
الحل: تحقق من إعدادات قاعدة البيانات في config/database.php
```

#### صفحة 500 Internal Server Error
```
الحل: 
1. تحقق من ملف .htaccess
2. تأكد من صلاحيات المجلدات
3. راجع سجل أخطاء الخادم
```

#### الخطوط العربية لا تظهر
```
الحل: تأكد من ترميز UTF-8 في قاعدة البيانات والملفات
```

#### البريد الإلكتروني لا يُرسل
```
الحل: 
1. تحقق من إعدادات SMTP
2. تأكد من تفعيل دالة mail() في PHP
3. استخدم خدمة بريد خارجية مثل Gmail
```

## الصيانة والتحديث

### 1. النسخ الاحتياطية الدورية
```bash
# إنشاء نسخة احتياطية يومية
0 2 * * * mysqldump -u user -p database > /backup/db_$(date +\%Y\%m\%d).sql
```

### 2. مراقبة الأداء
- راقب استخدام الذاكرة والمعالج
- تحقق من سرعة تحميل الصفحات
- راجع سجلات الأخطاء بانتظام

### 3. تحديثات الأمان
- حدّث PHP وMySQL بانتظام
- راجع وحدّث كلمات المرور
- تحقق من التحديثات الأمنية

## الدعم الفني

### معلومات الاتصال
- **الموقع**: https://nakra.host
- **البريد**: <EMAIL>
- **الشركة**: https://nakraformarketing.com

### الموارد المفيدة
- [دليل PHP](https://www.php.net/manual/en/)
- [دليل MySQL](https://dev.mysql.com/doc/)
- [دليل Apache](https://httpd.apache.org/docs/)

## الملاحظات المهمة

1. **احذف ملف install.php** بعد اكتمال التثبيت
2. **غيّر كلمات المرور الافتراضية** فوراً
3. **فعّل HTTPS** لحماية البيانات
4. **أنشئ نسخ احتياطية دورية**
5. **راقب سجلات الأخطاء** بانتظام

---

© 2024 نقره هوست - شركة نقرة للتسويق الإلكتروني
