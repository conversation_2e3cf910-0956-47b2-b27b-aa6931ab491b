# نقره هوست - Nakra Host

موقع إلكتروني احترافي لشركة نقره هوست المتخصصة في خدمات الاستضافة والبرمجة والتسويق الإلكتروني.

## نظرة عامة

نقره هوست هو موقع شامل يقدم خدمات متنوعة في مجال التكنولوجيا والتسويق الرقمي، مقدم من شركة نقرة للتسويق الإلكتروني بالشراكة مع هوست ميد.

## الخدمات المقدمة

### 🖥️ استضافة المواقع
- استضافة مشتركة أساسية ومتقدمة
- خوادم عالية الأداء مع تقنية SSD
- شهادات SSL مجانية
- نسخ احتياطية يومية
- دعم فني على مدار الساعة

### 💻 برمجة المواقع
- تطوير مواقع إلكترونية احترافية
- متاجر إلكترونية متجاوبة
- تطبيقات ويب مخصصة
- تحسين محركات البحث (SEO)

### 📢 التسويق الإلكتروني
- إعلانات جوجل (Google Ads)
- إعلانات فيسبوك (Facebook Ads)
- إعلانات تيك توك (TikTok Ads)
- إدارة وسائل التواصل الاجتماعي
- تحليل البيانات والتقارير

## المميزات التقنية

### 🎨 التصميم
- تصميم متجاوب مع جميع الأجهزة (Responsive Design)
- خطوط عربية احترافية (Cairo & Tajawal)
- واجهة مستخدم حديثة وسهلة الاستخدام
- ألوان متناسقة ومريحة للعين

### 🔧 التقنيات المستخدمة
- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Styling**: CSS Grid, Flexbox, Custom Properties
- **Icons**: Font Awesome 6
- **Security**: PDO, Password Hashing, CSRF Protection

### 🛡️ الأمان
- تشفير كلمات المرور باستخدام PHP password_hash()
- حماية من SQL Injection باستخدام Prepared Statements
- تنظيف البيانات المدخلة (Data Sanitization)
- جلسات آمنة (Secure Sessions)
- التحقق من البريد الإلكتروني

## هيكل المشروع

```
nakra-host/
├── config/
│   ├── config.php          # الإعدادات العامة
│   └── database.php        # إعدادات قاعدة البيانات
├── classes/
│   └── User.php           # فئة إدارة المستخدمين
├── database/
│   └── schema.sql         # هيكل قاعدة البيانات
├── assets/
│   ├── css/
│   │   └── style.css      # التصميم الأساسي
│   └── js/
│       └── main.js        # JavaScript الأساسي
├── auth/
│   ├── login.php          # تسجيل الدخول
│   ├── register.php       # إنشاء حساب
│   └── logout.php         # تسجيل الخروج
├── dashboard/
│   ├── index.php          # لوحة التحكم الرئيسية
│   └── orders.php         # إدارة الطلبات
├── index.php              # الصفحة الرئيسية
├── services.php           # صفحة الخدمات
├── order.php              # صفحة طلب الخدمات
└── README.md              # هذا الملف
```

## قاعدة البيانات

### الجداول الرئيسية

#### users - المستخدمين
- إدارة حسابات العملاء والمديرين
- تشفير كلمات المرور
- التحقق من البريد الإلكتروني
- إعادة تعيين كلمة المرور

#### service_categories - فئات الخدمات
- تصنيف الخدمات (استضافة، برمجة، تسويق)
- ترتيب وإدارة الفئات

#### services - الخدمات
- تفاصيل كل خدمة
- الأسعار ودورات الفوترة
- المميزات والوصف

#### orders - الطلبات
- طلبات العملاء
- حالة الطلب ومتابعته
- ربط بالخدمات والمستخدمين

#### invoices - الفواتير
- إدارة الفواتير والمدفوعات
- تواريخ الاستحقاق
- حالة الدفع

#### support_tickets - تذاكر الدعم
- نظام دعم فني متكامل
- تصنيف الأولويات
- متابعة الحالة

## التثبيت والإعداد

### المتطلبات
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx Web Server
- mod_rewrite enabled

### خطوات التثبيت

1. **رفع الملفات**
   ```bash
   # رفع جميع ملفات المشروع إلى مجلد الاستضافة
   ```

2. **إنشاء قاعدة البيانات**
   ```sql
   # تشغيل ملف database/schema.sql
   mysql -u username -p database_name < database/schema.sql
   ```

3. **تحديث إعدادات قاعدة البيانات**
   ```php
   // في ملف config/database.php
   private $host = 'localhost';
   private $dbname = 'your_database_name';
   private $username = 'your_username';
   private $password = 'your_password';
   ```

4. **تحديث الإعدادات العامة**
   ```php
   // في ملف config/config.php
   define('SITE_URL', 'https://nakra.host');
   define('SITE_EMAIL', '<EMAIL>');
   // ... باقي الإعدادات
   ```

5. **ضبط صلاحيات المجلدات**
   ```bash
   chmod 755 uploads/
   chmod 644 config/*.php
   ```

## الاستخدام

### للعملاء
1. إنشاء حساب جديد
2. تصفح الخدمات المتاحة
3. طلب الخدمة المناسبة
4. متابعة الطلبات من لوحة التحكم
5. إدارة الفواتير والمدفوعات

### للمديرين
1. تسجيل الدخول بحساب إداري
2. إدارة الخدمات والأسعار
3. متابعة الطلبات والعملاء
4. الرد على تذاكر الدعم
5. إدارة المحتوى والإعدادات

## الأمان والحماية

- **تشفير البيانات**: جميع كلمات المرور مشفرة
- **حماية SQL**: استخدام Prepared Statements
- **تنظيف البيانات**: تنظيف جميع المدخلات
- **جلسات آمنة**: إدارة آمنة للجلسات
- **HTTPS**: دعم شهادات SSL

## الدعم والصيانة

### النسخ الاحتياطية
- نسخ احتياطية يومية لقاعدة البيانات
- نسخ احتياطية أسبوعية للملفات
- اختبار دوري لاستعادة البيانات

### المراقبة
- مراقبة أداء الخادم
- تسجيل الأخطاء والمشاكل
- تحديثات أمنية دورية

## التطوير المستقبلي

### المميزات المخططة
- [ ] نظام دفع إلكتروني متكامل
- [ ] تطبيق محمول
- [ ] لوحة تحكم متقدمة للمديرين
- [ ] نظام تذاكر دعم محسن
- [ ] تقارير وإحصائيات تفصيلية
- [ ] نظام إحالة وعمولات
- [ ] دعم متعدد اللغات

## الترخيص

هذا المشروع مطور خصيصاً لشركة نقرة للتسويق الإلكتروني وشركة نقره هوست.

## التواصل

- **الموقع**: https://nakra.host
- **البريد الإلكتروني**: <EMAIL>
- **الشركة الأم**: https://nakraformarketing.com
- **الشريك**: https://hostmeed.cloud

---

© 2024 نقره هوست - شركة نقرة للتسويق الإلكتروني. جميع الحقوق محفوظة.
