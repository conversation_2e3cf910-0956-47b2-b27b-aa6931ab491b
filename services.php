<?php
require_once 'config/config.php';

// استرجاع الخدمات من قاعدة البيانات
try {
    $db = Database::getInstance();
    
    // استرجاع فئات الخدمات
    $stmt = $db->query("SELECT * FROM service_categories WHERE status = 'active' ORDER BY sort_order");
    $categories = $stmt->fetchAll();
    
    // استرجاع الخدمات
    $stmt = $db->query("
        SELECT s.*, c.name as category_name, c.icon as category_icon 
        FROM services s 
        JOIN service_categories c ON s.category_id = c.id 
        WHERE s.status = 'active' 
        ORDER BY c.sort_order, s.sort_order
    ");
    $services = $stmt->fetchAll();
    
    // تجميع الخدمات حسب الفئة
    $servicesByCategory = [];
    foreach ($services as $service) {
        $servicesByCategory[$service['category_id']][] = $service;
    }
    
} catch (Exception $e) {
    $categories = [];
    $services = [];
    $servicesByCategory = [];
}

$message = getMessage();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خدماتنا - <?php echo SITE_NAME; ?></title>
    <meta name="description" content="تعرف على جميع خدمات نقره هوست من استضافة المواقع وبرمجة المواقع والتسويق الإلكتروني">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .services-hero {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 3rem 0;
            text-align: center;
        }
        
        .category-section {
            padding: 3rem 0;
        }
        
        .category-section:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .category-header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .category-icon {
            font-size: 4rem;
            color: var(--secondary-color);
            margin-bottom: 1rem;
        }
        
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
        }
        
        .service-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            overflow: hidden;
            transition: var(--transition);
        }
        
        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }
        
        .service-header {
            padding: 2rem;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            text-align: center;
        }
        
        .service-price {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .service-billing {
            opacity: 0.9;
            font-size: 0.9rem;
        }
        
        .service-body {
            padding: 2rem;
        }
        
        .service-features {
            list-style: none;
            margin-bottom: 2rem;
        }
        
        .service-features li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .service-features li:last-child {
            border-bottom: none;
        }
        
        .service-features i {
            color: var(--success-color);
            margin-left: 0.5rem;
        }
        
        .service-footer {
            padding: 0 2rem 2rem;
            text-align: center;
        }
        
        .order-btn {
            width: 100%;
            padding: 1rem;
            font-size: 1.1rem;
            font-weight: 600;
        }
        
        .pricing-note {
            background: var(--light-color);
            padding: 2rem;
            border-radius: var(--border-radius);
            text-align: center;
            margin: 3rem 0;
        }
        
        @media (max-width: 768px) {
            .services-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- الرأس -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="index.php" class="logo">
                    <i class="fas fa-server"></i> نقره هوست
                </a>
                
                <ul class="nav-menu" id="navMenu">
                    <li><a href="index.php">الرئيسية</a></li>
                    <li><a href="services.php">خدماتنا</a></li>
                    <li><a href="hosting.php">الاستضافة</a></li>
                    <li><a href="development.php">البرمجة</a></li>
                    <li><a href="marketing.php">التسويق</a></li>
                    <li><a href="support.php">الدعم</a></li>
                    <li><a href="contact.php">اتصل بنا</a></li>
                    <?php if (isLoggedIn()): ?>
                        <li><a href="dashboard/">لوحة التحكم</a></li>
                        <li><a href="auth/logout.php">تسجيل الخروج</a></li>
                    <?php else: ?>
                        <li><a href="auth/login.php">تسجيل الدخول</a></li>
                        <li><a href="auth/register.php">إنشاء حساب</a></li>
                    <?php endif; ?>
                </ul>
                
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </nav>
        </div>
    </header>

    <!-- قسم البطل -->
    <section class="services-hero">
        <div class="container">
            <h1>خدماتنا المتميزة</h1>
            <p>نقدم مجموعة شاملة من الخدمات التقنية لتلبية جميع احتياجاتك الرقمية</p>
        </div>
    </section>

    <!-- عرض الرسائل -->
    <?php if ($message): ?>
        <div class="container mt-2">
            <div class="alert alert-<?php echo $message['type']; ?>">
                <?php echo $message['message']; ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- أقسام الخدمات -->
    <?php foreach ($categories as $category): ?>
        <section class="category-section">
            <div class="container">
                <div class="category-header">
                    <div class="category-icon">
                        <i class="<?php echo htmlspecialchars($category['icon']); ?>"></i>
                    </div>
                    <h2><?php echo htmlspecialchars($category['name']); ?></h2>
                    <p><?php echo htmlspecialchars($category['description']); ?></p>
                </div>
                
                <?php if (isset($servicesByCategory[$category['id']])): ?>
                    <div class="services-grid">
                        <?php foreach ($servicesByCategory[$category['id']] as $service): ?>
                            <div class="service-card">
                                <div class="service-header">
                                    <h3><?php echo htmlspecialchars($service['name']); ?></h3>
                                    <div class="service-price">
                                        <?php echo number_format($service['price'], 0); ?> <?php echo CURRENCY_SYMBOL; ?>
                                    </div>
                                    <div class="service-billing">
                                        <?php
                                        $billingTexts = [
                                            'monthly' => 'شهرياً',
                                            'quarterly' => 'كل 3 أشهر',
                                            'semi_annual' => 'كل 6 أشهر',
                                            'annual' => 'سنوياً'
                                        ];
                                        echo $billingTexts[$service['billing_cycle']] ?? 'شهرياً';
                                        ?>
                                    </div>
                                </div>
                                
                                <div class="service-body">
                                    <p><?php echo htmlspecialchars($service['description']); ?></p>
                                    
                                    <?php if ($service['features']): ?>
                                        <ul class="service-features">
                                            <?php
                                            $features = explode("\n", $service['features']);
                                            foreach ($features as $feature):
                                                $feature = trim($feature);
                                                if (!empty($feature)):
                                            ?>
                                                <li>
                                                    <i class="fas fa-check"></i>
                                                    <?php echo htmlspecialchars($feature); ?>
                                                </li>
                                            <?php 
                                                endif;
                                            endforeach; 
                                            ?>
                                        </ul>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="service-footer">
                                    <?php if (isLoggedIn()): ?>
                                        <a href="order.php?service_id=<?php echo $service['id']; ?>" class="btn btn-primary order-btn">
                                            <i class="fas fa-shopping-cart"></i> اطلب الآن
                                        </a>
                                    <?php else: ?>
                                        <a href="auth/login.php" class="btn btn-primary order-btn">
                                            <i class="fas fa-sign-in-alt"></i> سجل دخولك للطلب
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center">
                        <p>لا توجد خدمات متاحة في هذه الفئة حالياً</p>
                    </div>
                <?php endif; ?>
            </div>
        </section>
    <?php endforeach; ?>

    <!-- ملاحظة الأسعار -->
    <section class="section">
        <div class="container">
            <div class="pricing-note">
                <h3><i class="fas fa-info-circle"></i> ملاحظات مهمة</h3>
                <p>جميع الأسعار المعروضة بالريال السعودي وتشمل ضريبة القيمة المضافة</p>
                <p>يمكنك التواصل معنا للحصول على عروض خاصة وخصومات للعملاء الجدد</p>
                <p>نقدم ضمان استرداد المال خلال 30 يوم من تاريخ الطلب</p>
            </div>
        </div>
    </section>

    <!-- قسم الدعم -->
    <section class="section" style="background-color: #f8f9fa;">
        <div class="container text-center">
            <h2>هل تحتاج مساعدة في اختيار الخدمة المناسبة؟</h2>
            <p>فريقنا المتخصص جاهز لمساعدتك في اختيار الخدمة التي تناسب احتياجاتك</p>
            <div class="mt-3">
                <a href="contact.php" class="btn btn-primary">
                    <i class="fas fa-phone"></i> تواصل معنا
                </a>
                <a href="support.php" class="btn btn-outline">
                    <i class="fas fa-headset"></i> الدعم الفني
                </a>
            </div>
        </div>
    </section>

    <!-- التذييل -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>نقره هوست</h3>
                    <p>شريكك الموثوق في عالم الاستضافة والبرمجة والتسويق الإلكتروني</p>
                    <p>خدمات مقدمة من <a href="<?php echo COMPANY_WEBSITE; ?>" target="_blank">شركة نقرة للتسويق الإلكتروني</a></p>
                </div>
                
                <div class="footer-section">
                    <h3>خدماتنا</h3>
                    <ul style="list-style: none;">
                        <li><a href="hosting.php">استضافة المواقع</a></li>
                        <li><a href="development.php">برمجة المواقع</a></li>
                        <li><a href="marketing.php">التسويق الإلكتروني</a></li>
                        <li><a href="support.php">الدعم الفني</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3>روابط مهمة</h3>
                    <ul style="list-style: none;">
                        <li><a href="about.php">من نحن</a></li>
                        <li><a href="contact.php">اتصل بنا</a></li>
                        <li><a href="terms.php">الشروط والأحكام</a></li>
                        <li><a href="privacy.php">سياسة الخصوصية</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3>تواصل معنا</h3>
                    <p><i class="fas fa-envelope"></i> <?php echo SITE_EMAIL; ?></p>
                    <p><i class="fas fa-phone"></i> <?php echo SITE_PHONE; ?></p>
                    <div class="mt-2">
                        <a href="#" style="margin-left: 10px;"><i class="fab fa-facebook fa-2x"></i></a>
                        <a href="#" style="margin-left: 10px;"><i class="fab fa-twitter fa-2x"></i></a>
                        <a href="#" style="margin-left: 10px;"><i class="fab fa-instagram fa-2x"></i></a>
                        <a href="#"><i class="fab fa-linkedin fa-2x"></i></a>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
</body>
</html>
