<?php
require_once '../config/config.php';
require_once '../classes/User.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../auth/login.php');
}

$user = new User();
$userData = $user->getUserById($_SESSION['user_id']);

// استرجاع إحصائيات المستخدم
try {
    $db = Database::getInstance();
    
    // عدد الطلبات
    $stmt = $db->query("SELECT COUNT(*) as count FROM orders WHERE user_id = ?", [$_SESSION['user_id']]);
    $ordersCount = $stmt->fetch()['count'];
    
    // عدد الفواتير غير المدفوعة
    $stmt = $db->query("SELECT COUNT(*) as count FROM invoices WHERE user_id = ? AND status = 'unpaid'", [$_SESSION['user_id']]);
    $unpaidInvoicesCount = $stmt->fetch()['count'];
    
    // عدد تذاكر الدعم المفتوحة
    $stmt = $db->query("SELECT COUNT(*) as count FROM support_tickets WHERE user_id = ? AND status != 'closed'", [$_SESSION['user_id']]);
    $openTicketsCount = $stmt->fetch()['count'];
    
    // آخر الطلبات
    $stmt = $db->query("
        SELECT o.*, s.name as service_name 
        FROM orders o 
        JOIN services s ON o.service_id = s.id 
        WHERE o.user_id = ? 
        ORDER BY o.created_at DESC 
        LIMIT 5
    ", [$_SESSION['user_id']]);
    $recentOrders = $stmt->fetchAll();
    
} catch (Exception $e) {
    $ordersCount = 0;
    $unpaidInvoicesCount = 0;
    $openTicketsCount = 0;
    $recentOrders = [];
}

$message = getMessage();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .dashboard-container {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: var(--primary-color);
            color: white;
            padding: 2rem 0;
        }
        
        .sidebar-header {
            text-align: center;
            padding: 0 1rem 2rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 2rem;
        }
        
        .sidebar-menu {
            list-style: none;
        }
        
        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }
        
        .sidebar-menu a {
            display: block;
            padding: 1rem 1.5rem;
            color: white;
            text-decoration: none;
            transition: var(--transition);
        }
        
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background-color: var(--secondary-color);
        }
        
        .main-content {
            flex: 1;
            padding: 2rem;
            background-color: #f8f9fa;
        }
        
        .dashboard-header {
            background: white;
            padding: 1.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            text-align: center;
        }
        
        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .recent-orders {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            overflow: hidden;
        }
        
        .recent-orders-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            background-color: var(--light-color);
        }
        
        .mobile-sidebar-toggle {
            display: none;
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 1001;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.75rem;
            border-radius: 50%;
            cursor: pointer;
        }
        
        @media (max-width: 768px) {
            .dashboard-container {
                flex-direction: column;
            }
            
            .sidebar {
                position: fixed;
                top: 0;
                right: -250px;
                height: 100vh;
                z-index: 1000;
                transition: right 0.3s ease;
            }
            
            .sidebar.active {
                right: 0;
            }
            
            .mobile-sidebar-toggle {
                display: block;
            }
            
            .main-content {
                padding: 4rem 1rem 1rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- الشريط الجانبي -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-user-circle"></i></h3>
                <p><?php echo htmlspecialchars($_SESSION['full_name']); ?></p>
                <small><?php echo htmlspecialchars($_SESSION['email']); ?></small>
            </div>
            
            <ul class="sidebar-menu">
                <li><a href="index.php" class="active"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                <li><a href="orders.php"><i class="fas fa-shopping-cart"></i> طلباتي</a></li>
                <li><a href="invoices.php"><i class="fas fa-file-invoice"></i> الفواتير</a></li>
                <li><a href="support.php"><i class="fas fa-headset"></i> الدعم الفني</a></li>
                <li><a href="profile.php"><i class="fas fa-user-edit"></i> الملف الشخصي</a></li>
                <li><a href="../services.php"><i class="fas fa-plus"></i> طلب خدمة جديدة</a></li>
                <li><a href="../index.php"><i class="fas fa-home"></i> الموقع الرئيسي</a></li>
                <li><a href="../auth/logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
            </ul>
        </aside>
        
        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <button class="mobile-sidebar-toggle" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
            
            <!-- رأس لوحة التحكم -->
            <div class="dashboard-header">
                <h1><i class="fas fa-tachometer-alt"></i> لوحة التحكم</h1>
                <p>مرحباً بك <?php echo htmlspecialchars($_SESSION['full_name']); ?>، إليك نظرة عامة على حسابك</p>
            </div>
            
            <!-- عرض الرسائل -->
            <?php if ($message): ?>
                <div class="alert alert-<?php echo $message['type']; ?>">
                    <?php echo $message['message']; ?>
                </div>
            <?php endif; ?>
            
            <!-- الإحصائيات -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon" style="color: var(--secondary-color);">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="stat-number"><?php echo $ordersCount; ?></div>
                    <div>إجمالي الطلبات</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon" style="color: var(--warning-color);">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="stat-number"><?php echo $unpaidInvoicesCount; ?></div>
                    <div>فواتير غير مدفوعة</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon" style="color: var(--danger-color);">
                        <i class="fas fa-headset"></i>
                    </div>
                    <div class="stat-number"><?php echo $openTicketsCount; ?></div>
                    <div>تذاكر دعم مفتوحة</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon" style="color: var(--success-color);">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <div class="stat-number"><?php echo $userData['email_verified'] ? 'مفعل' : 'غير مفعل'; ?></div>
                    <div>حالة الحساب</div>
                </div>
            </div>
            
            <!-- آخر الطلبات -->
            <div class="recent-orders">
                <div class="recent-orders-header">
                    <h3><i class="fas fa-clock"></i> آخر الطلبات</h3>
                </div>
                
                <?php if (empty($recentOrders)): ?>
                    <div class="p-3 text-center">
                        <i class="fas fa-inbox fa-3x" style="color: var(--border-color); margin-bottom: 1rem;"></i>
                        <p>لا توجد طلبات حتى الآن</p>
                        <a href="../services.php" class="btn btn-primary">طلب خدمة جديدة</a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>رقم الطلب</th>
                                    <th>الخدمة</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentOrders as $order): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($order['order_number']); ?></td>
                                        <td><?php echo htmlspecialchars($order['service_name']); ?></td>
                                        <td><?php echo number_format($order['total_amount'], 2); ?> <?php echo CURRENCY_SYMBOL; ?></td>
                                        <td>
                                            <?php
                                            $statusColors = [
                                                'pending' => 'warning',
                                                'processing' => 'info',
                                                'active' => 'success',
                                                'suspended' => 'danger',
                                                'cancelled' => 'secondary'
                                            ];
                                            $statusTexts = [
                                                'pending' => 'في الانتظار',
                                                'processing' => 'قيد المعالجة',
                                                'active' => 'نشط',
                                                'suspended' => 'معلق',
                                                'cancelled' => 'ملغي'
                                            ];
                                            $color = $statusColors[$order['status']] ?? 'secondary';
                                            $text = $statusTexts[$order['status']] ?? $order['status'];
                                            ?>
                                            <span class="badge badge-<?php echo $color; ?>"><?php echo $text; ?></span>
                                        </td>
                                        <td><?php echo date('Y/m/d', strtotime($order['created_at'])); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="p-3 text-center">
                        <a href="orders.php" class="btn btn-outline">عرض جميع الطلبات</a>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>
    
    <script src="../assets/js/main.js"></script>
    <script>
        // تفعيل الشريط الجانبي المحمول
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            document.getElementById('sidebar').classList.toggle('active');
        });
        
        // إغلاق الشريط الجانبي عند النقر خارجه
        document.addEventListener('click', function(e) {
            const sidebar = document.getElementById('sidebar');
            const toggle = document.getElementById('sidebarToggle');
            
            if (!sidebar.contains(e.target) && !toggle.contains(e.target)) {
                sidebar.classList.remove('active');
            }
        });
    </script>
    
    <style>
        .badge {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 600;
            color: white;
        }
        
        .badge-warning { background-color: var(--warning-color); }
        .badge-info { background-color: var(--secondary-color); }
        .badge-success { background-color: var(--success-color); }
        .badge-danger { background-color: var(--danger-color); }
        .badge-secondary { background-color: #6c757d; }
        
        .table-responsive {
            overflow-x: auto;
        }
    </style>
</body>
</html>
