<?php
/**
 * إعدادات الموقع العامة - نقره هوست
 * General Site Configuration - Nakra Host
 */

// إعدادات الموقع الأساسية
define('SITE_NAME', 'نقره هوست');
define('SITE_URL', 'https://nakra.host');
define('SITE_EMAIL', '<EMAIL>');
define('SITE_PHONE', '+966123456789');

// إعدادات الشركة
define('COMPANY_NAME', 'شركة نقرة للتسويق الإلكتروني');
define('COMPANY_WEBSITE', 'https://nakraformarketing.com');
define('HOSTMEED_URL', 'https://hostmeed.cloud');

// إعدادات الأمان
define('ENCRYPTION_KEY', 'your-secret-encryption-key-here');
define('SESSION_TIMEOUT', 3600); // ساعة واحدة

// إعدادات البريد الإلكتروني
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-email-password');
define('SMTP_ENCRYPTION', 'tls');

// إعدادات الملفات
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 5242880); // 5MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx']);

// إعدادات الصفحات
define('ITEMS_PER_PAGE', 20);

// إعدادات العملة
define('CURRENCY', 'SAR');
define('CURRENCY_SYMBOL', 'ر.س');

// إعدادات اللغة
define('DEFAULT_LANGUAGE', 'ar');
define('TIMEZONE', 'Asia/Riyadh');

// تعيين المنطقة الزمنية
date_default_timezone_set(TIMEZONE);

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تضمين ملف قاعدة البيانات
require_once __DIR__ . '/database.php';

// دالة لتنظيف البيانات
function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

// دالة للتحقق من تسجيل الدخول
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

// دالة للتحقق من صلاحيات الإدارة
function isAdmin() {
    return isLoggedIn() && isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

// دالة لإعادة التوجيه
function redirect($url) {
    header("Location: $url");
    exit();
}

// دالة لعرض الرسائل
function showMessage($message, $type = 'info') {
    $_SESSION['message'] = $message;
    $_SESSION['message_type'] = $type;
}

// دالة لاسترجاع وحذف الرسائل
function getMessage() {
    if (isset($_SESSION['message'])) {
        $message = $_SESSION['message'];
        $type = $_SESSION['message_type'] ?? 'info';
        unset($_SESSION['message'], $_SESSION['message_type']);
        return ['message' => $message, 'type' => $type];
    }
    return null;
}
